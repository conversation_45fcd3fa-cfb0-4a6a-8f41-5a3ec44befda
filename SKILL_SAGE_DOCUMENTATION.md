# Skill Sage - AI-Powered Job Matching Platform

## Table of Contents

1. [Overview](#overview)
2. [System Architecture](#system-architecture)
3. [Technologies Used](#technologies-used)
4. [User Journey & Features](#user-journey--features)
5. [Backend API Documentation](#backend-api-documentation)
6. [Frontend Applications](#frontend-applications)
7. [AI & Machine Learning Features](#ai--machine-learning-features)
8. [Installation & Setup](#installation--setup)
9. [API Endpoints](#api-endpoints)
10. [Deployment](#deployment)

## Overview

Skill Sage is a comprehensive AI-powered job matching platform that leverages natural language processing (NLP), machine learning, and advanced recommendation systems to connect job seekers with relevant opportunities. The platform consists of three main components:

- **Flutter Mobile App**: Cross-platform mobile application for job seekers
- **React Dashboard**: Administrative web interface for job management
- **FastAPI Backend**: High-performance API server with AI-powered matching algorithms

### Key Features

- **AI-Powered Job Matching**: Advanced recommendation system using clustering and Jaccard similarity
- **Resume Analysis**: Intelligent parsing and skill extraction from resumes
- **Real-time Job Scraping**: Automated job collection from multiple sources
- **Skill Transferability Analysis**: Graph theory-based skill mapping across occupations
- **Hybrid Recommendation System (HDCF)**: Deep learning-enhanced matching with semantic context

## System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Flutter App   │    │ React Dashboard │    │   Web Scrapers  │
│   (Job Seekers) │    │ (Administrators)│    │  (External APIs)│
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │     FastAPI Backend       │
                    │   (Port 8004)             │
                    │  ┌─────────────────────┐  │
                    │  │   AI/ML Services    │  │
                    │  │  - LLM (Gemini 2.0) │  │
                    │  │  - Job Matching     │  │
                    │  │  - Skill Analysis   │  │
                    │  └─────────────────────┘  │
                    └─────────────┬─────────────┘
                                  │
                    ┌─────────────┴─────────────┐
                    │    PostgreSQL Database    │
                    │   (User Data & Jobs)      │
                    └───────────────────────────┘
```

## Technologies Used

### Backend Technologies

- **FastAPI**: Modern, high-performance web framework for building APIs
- **Python 3.7+**: Core programming language
- **PostgreSQL**: Primary database for data persistence
- **SQLAlchemy**: ORM for database operations
- **Alembic**: Database migration management
- **Uvicorn**: ASGI server for FastAPI
- **WebSockets**: Real-time communication
- **APScheduler**: Job scheduling for automated tasks

### AI & Machine Learning

- **Gemini 2.0 Flash**: Google's Large Language Model for intelligent processing
- **Natural Language Processing (NLP)**: Text analysis and skill extraction
- **Clustering Algorithms**: Job categorization and matching
- **Jaccard Similarity**: Skill overlap calculation
- **Graph Theory**: Skill transferability analysis using centrality metrics
- **Hybrid Deep Collaborative Filtering (HDCF)**: Advanced recommendation system

### Frontend Technologies

#### Flutter Mobile App

- **Flutter**: Cross-platform mobile development framework
- **Dart**: Programming language for Flutter
- **Riverpod**: State management solution
- **Dio**: HTTP client for API communication
- **WebView Flutter**: Embedded web content
- **YouTube Player**: Video content integration with AI Recommendation
- **File Picker**: CV upload functionality
- **Image Picker**: Photo capture and selection
- **Shared Preferences**: Local data storage
- **WebSocket Channel**: Real-time communication

#### React Dashboard

- **React**: Frontend library for building user interfaces
- **Create React App**: Development environment
- **JavaScript/TypeScript**: Programming languages
- **CSS3**: Styling and responsive design
- **Axios**: HTTP client for API requests

### External Integrations

- **Job Scraping Sources**:
  - RemoteOK
  - We Work Remotely
  - StackOverflow (API)
  - Greenhouse
  - Business Ghana
- **YouTube API**: Educational content integration
- **PDF Processing**: Resume parsing and analysis

## User Journey & Features

### 1. User Registration & Authentication

```
Registration → Email Verification → Profile Setup → Skill Assessment
```

**Features:**

- Email-based registration with password hashing
- JWT token-based authentication
- Profile completion with skills, experience, and education
- Resume upload with AI-powered parsing

### 2. Profile Management

```
Personal Info → Skills → Experience → Education → Resume Upload
```

**Skills Management:**

- Add/remove technical and soft skills
- Skill level assessment (Beginner, Intermediate, Advanced)
- Skill search and auto-completion
- Skill categorization and tagging

**Experience Tracking:**

- Work history with detailed descriptions
- Project portfolios
- Achievement tracking
- Career progression mapping

**Education Records:**

- Academic qualifications
- Certifications and courses
- Continuous learning tracking

### 3. Resume Processing & Analysis

```
Resume Upload → AI Parsing → Skill Extraction → Profile Enhancement
```

**AI-Powered Features:**

- Automatic skill extraction from resume text
- Experience level assessment
- Education qualification recognition
- Skill gap analysis
- Profile optimization suggestions

### 4. Job Discovery & Matching

```
Job Scraping → AI Matching → Personalized Recommendations → Application Tracking
```

**Job Sources:**

- Real-time scraping from 5+ job boards
- Internal job postings
- External API integrations
- Manual job posting by administrators

**Matching Algorithm:**

- **Skill Similarity**: Jaccard similarity for skill overlap
- **Experience Matching**: Career level alignment
- **Location Preferences**: Geographic filtering
- **Salary Expectations**: Compensation range matching
- **Company Culture**: Cultural fit assessment

### 5. Job Application & Tracking

```
Job Discovery → Detailed Analysis → Application → Status Tracking
```

**Application Features:**

- One-click job applications
- Application status tracking
- Interview scheduling
- Follow-up reminders
- Application history

### 6. Learning & Development

```
Skill Gap Analysis → Course Recommendations → Video Learning → Progress Tracking
```

**Educational Content:**

- Curated courses based on skill gaps
- YouTube video integration
- Learning path recommendations
- Progress tracking and certificates

## Backend API Documentation

### Core Services

#### 1. User Management (`/routes/user_routes.py`)

- **User Registration & Authentication**
- **Profile Management**
- **Skill Management**
- **Resume Processing**
- **Job Preferences**

#### 2. Job Management (`/routes/job.py`)

- **Job Scraping & Storage**
- **Job Recommendations**
- **Application Management**
- **Bookmark System**

#### 3. Course Management (`/routes/courses.py`)

- **Course Discovery**
- **Skill-based Recommendations**
- **Learning Progress Tracking**

#### 4. Authentication (`/routes/auth.py`)

- **JWT Token Management**
- **Password Hashing**
- **Session Management**

### AI/ML Services

#### 1. LLM Service (`/services/llm.py`)

```python
class BaseLLMClient:
    - Gemini 2.0 Flash integration
    - API key rotation for reliability
    - Content generation for job descriptions
    - Skill analysis and matching
```

#### 2. Job Scraper (`/services/job_scraper.py`)

```python
class JobScraper:
    - Multi-source job collection
    - Data normalization and cleaning
    - Duplicate detection and removal
    - Scheduled scraping with APScheduler
```

#### 3. Enhanced Matching System (`/services/enhanced_matching_system.py`)

```python
class EnhancedMatchingSystem:
    - Hybrid Deep Collaborative Filtering (HDCF)
    - Semantic context integration
    - Skill transferability analysis
    - Real-time matching algorithms
```

## Frontend Applications

### Flutter Mobile App Structure

#### Core Screens

- **Authentication**: Login, Register, Forgot Password
- **Dashboard**: Personalized job recommendations
- **Profile**: User information and skill management
- **Jobs**: Job listings and detailed analysis
- **Applications**: Application tracking and history
- **Courses**: Learning content and progress
- **Settings**: App preferences and notifications

#### State Management (Riverpod)

```dart
- userProvider: User authentication and profile data
- jobProvider: Job listings and applications
- courseProvider: Course content and progress
- settingsProvider: App preferences and themes
```

#### Key Features

- **Offline Support**: Local data caching with SharedPreferences
- **Real-time Updates**: WebSocket integration for live notifications
- **File Upload**: CV and document management with AI Recommendation
- **Video Integration**: YouTube Videos with AI Recommendation
- **Responsive Design**: Adaptive UI for different screen sizes

### React Dashboard Structure

#### Admin Features

- **Job Management**: Enable/disable job postings
- **User Analytics**: User engagement and statistics
- **Content Management**: Course and video management
- **System Monitoring**: API performance and error tracking
- **Data Export**: User and job data export functionality

## AI & Machine Learning Features

### 1. Natural Language Processing (NLP)

- **Resume Parsing**: Intelligent extraction of skills, experience, and education
- **Job Description Analysis**: Automated categorization and skill requirement extraction
- **Semantic Matching**: Understanding context and meaning beyond keyword matching

### 2. Clustering and Similarity Analysis

- **Job Clustering**: Grouping similar job opportunities
- **Skill Clustering**: Identifying related skill sets
- **User Segmentation**: Categorizing users based on profiles and preferences

### 3. Graph Theory Implementation

- **Skill Transferability**: Analyzing how skills transfer between occupations
- **Centrality Metrics**: Identifying core skills and career paths
- **Network Analysis**: Understanding skill relationships and dependencies

### 4. Hybrid Deep Collaborative Filtering (HDCF)

- **Deep Learning Integration**: Neural networks for complex pattern recognition
- **Collaborative Filtering**: User behavior-based recommendations
- **Semantic Context**: Understanding job requirements and user capabilities
- **Real-time Adaptation**: Dynamic model updates based on user interactions

## Installation & Setup

### Backend Setup (FastAPI)

1. **Clone the repository**

```bash
git clone <repository-url>
cd skill_sage
```

2. **Create virtual environment**

```bash
python3 -m venv venv
source venv/bin/activate  # On Windows: .\venv\Scripts\activate
```

3. **Install dependencies**

```bash
pip install -r requirements.txt
```

4. **Environment Configuration**

```bash
# Create .env file with required variables
GEMINI_API_KEY_01=your_gemini_api_key_1
GEMINI_API_KEY_02=your_gemini_api_key_2
DATABASE_URL=postgresql://user:password@localhost/skill_sage
SECRET_KEY=your_secret_key
```

5. **Database Setup**

```bash
# Initialize database
python -c "from db.connection import initDB; initDB()"

# Run migrations (if any)
alembic upgrade head
```

6. **Start the server**

```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8004
```

### Flutter App Setup

1. **Navigate to Flutter directory**

```bash
cd Skill-Sage
```

2. **Install dependencies**

```bash
flutter pub get
```

3. **Configure environment**

```bash
# Create .env file in assets folder
API_BASE_URL=http://localhost:8004
```

4. **Run the app**

```bash
flutter run
```

### React Dashboard Setup

1. **Navigate to dashboard directory**

```bash
cd dashboard
```

2. **Install dependencies**

```bash
npm install
```

3. **Start development server**

```bash
npm start
```

## API Endpoints

### Authentication Endpoints

```
POST /auth/register     - User registration
POST /auth/login        - User login
POST /auth/logout       - User logout
POST /auth/refresh      - Token refresh
```

### User Management

```
GET  /user/             - Get user profile
PUT  /user/profile      - Update user profile
POST /user/skills       - Add skills
DELETE /user/skills     - Remove skills
POST /user/upload_resume - Upload resume
GET  /user/preferences  - Get user preferences
PATCH /user/preferences - Update preferences
```

### Job Management

```
GET  /job/              - Get all jobs
GET  /job/{id}          - Get specific job
POST /job/bookmark/{id} - Bookmark job
DELETE /job/bookmark/{id} - Remove bookmark
POST /job/application/{id} - Apply for job
GET  /job/recommendations - Get job recommendations
```

### Course Management

```
GET  /course/search/{skill} - Search courses by skill
GET  /course/{id}       - Get course details
POST /course/progress   - Update learning progress
```

### Admin Endpoints

```
GET  /admin/jobs        - Get all jobs (admin)
POST /admin/jobs/enable - Enable job posting
POST /admin/jobs/disable - Disable job posting
GET  /admin/users       - Get user statistics
POST /admin/scrape_jobs - Trigger job scraping
```

## Deployment

### Backend Deployment (Render/Railway)

```bash
# Build and deploy
pip install -r requirements.txt
uvicorn main:app --host 0.0.0.0 --port $PORT
```

### Flutter App Deployment

```bash
# Build APK
flutter build apk --release

# Build App Bundle
flutter build appbundle --release

# Deploy to Google Play Store
```

### React Dashboard Deployment (Netlify)

```bash
# Build for production
npm run build

# Deploy to Netlify
netlify deploy --prod --dir=build
```

## Key Algorithms & Implementation Details

### 1. Job Matching Algorithm

```python
def calculate_job_match_score(user_skills, job_requirements):
    # Jaccard similarity for skill overlap
    skill_overlap = len(user_skills & job_requirements) / len(user_skills | job_requirements)

    # Experience level matching
    experience_score = calculate_experience_match(user_experience, job_experience)

    # Location preference scoring
    location_score = calculate_location_match(user_location, job_location)

    # Weighted combination
    final_score = (skill_overlap * 0.5) + (experience_score * 0.3) + (location_score * 0.2)

    return final_score
```

### 2. Skill Transferability Analysis

```python
def analyze_skill_transferability(skill_graph):
    # Calculate centrality metrics
    betweenness_centrality = nx.betweenness_centrality(skill_graph)
    closeness_centrality = nx.closeness_centrality(skill_graph)

    # Identify transferable skills
    transferable_skills = []
    for skill, centrality in betweenness_centrality.items():
        if centrality > threshold:
            transferable_skills.append(skill)

    return transferable_skills
```

### 3. Hybrid Deep Collaborative Filtering

```python
class HDCFModel:
    def __init__(self):
        self.user_embedding = UserEmbeddingLayer()
        self.job_embedding = JobEmbeddingLayer()
        self.semantic_layer = SemanticContextLayer()
        self.fusion_layer = FusionLayer()

    def forward(self, user_features, job_features, context):
        user_vec = self.user_embedding(user_features)
        job_vec = self.job_embedding(job_features)
        context_vec = self.semantic_layer(context)

        # Fusion of embeddings
        combined = self.fusion_layer(user_vec, job_vec, context_vec)

        return torch.sigmoid(combined)
```

## Performance & Scalability

### Database Optimization

- **Indexing**: Strategic indexing on frequently queried columns
- **Connection Pooling**: Efficient database connection management
- **Query Optimization**: Optimized SQL queries with proper joins

### Caching Strategy

- **Redis Integration**: Session and API response caching
- **CDN**: Static asset delivery optimization
- **Browser Caching**: Client-side caching for improved performance

### Monitoring & Analytics

- **API Performance**: Response time monitoring
- **Error Tracking**: Comprehensive error logging and analysis
- **User Analytics**: Engagement and conversion tracking
- **System Health**: Resource utilization monitoring

## Security Implementation

### Authentication & Authorization

- **JWT Tokens**: Secure token-based authentication
- **Password Hashing**: bcrypt for password security
- **CORS Configuration**: Proper cross-origin resource sharing
- **Input Validation**: Comprehensive input sanitization

### Data Protection

- **Encryption**: Sensitive data encryption at rest and in transit
- **GDPR Compliance**: User data privacy and right to deletion
- **API Rate Limiting**: Protection against abuse and DDoS attacks

## Future Enhancements

### Planned Features

1. **Advanced AI Integration**: GPT-4 integration for enhanced matching
2. **Video Interviewing**: Built-in video interview capabilities
3. **Skill Assessment Tests**: Automated skill evaluation quizzes
4. **Career Path Planning**: AI-powered career trajectory recommendations
5. **Multi-language Support**: Internationalization and localization
6. **Blockchain Integration**: Verifiable credentials and certificates

### Technical Improvements

1. **Microservices Architecture**: Service decomposition for better scalability
2. **GraphQL API**: More efficient data fetching
3. **Real-time Chat**: Integrated messaging between employers and candidates
4. **Advanced Analytics**: Machine learning-powered insights and predictions

---

## Conclusion

Skill Sage represents a comprehensive solution for modern job matching, combining cutting-edge AI technologies with user-friendly interfaces. The platform's hybrid approach to recommendation systems, advanced NLP capabilities, and real-time job scraping make it a powerful tool for both job seekers and employers.

The modular architecture ensures scalability and maintainability, while the integration of multiple AI technologies provides sophisticated matching capabilities that go beyond simple keyword matching to understand context, skill transferability, and career progression potential.

For technical support, feature requests, or contributions, please refer to the project repositories or contact the development <NAME_EMAIL> and <EMAIL>.

Repository Backend:https://github.com/Emmanuel-Aggrey/skill_sage_backend
Repository Mobile:https://github.com/Emmanuel-Aggrey/skill_sage_mobile
Repository Dashboard:https://github.com/Emmanuel-Aggrey/skill_sage_dashboard

var Zd=e=>{throw TypeError(e)};var sc=(e,t,n)=>t.has(e)||Zd("Cannot "+n);var N=(e,t,n)=>(sc(e,t,"read from private field"),n?n.call(e):t.get(e)),W=(e,t,n)=>t.has(e)?Zd("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),G=(e,t,n,a)=>(sc(e,t,"write to private field"),a?a.call(e,n):t.set(e,n),n),Ze=(e,t,n)=>(sc(e,t,"access private method"),n);var Zi=(e,t,n,a)=>({set _(l){G(e,t,l,n)},get _(){return N(e,t,a)}});function ex(e,t){for(var n=0;n<t.length;n++){const a=t[n];if(typeof a!="string"&&!Array.isArray(a)){for(const l in a)if(l!=="default"&&!(l in e)){const r=Object.getOwnPropertyDescriptor(a,l);r&&Object.defineProperty(e,l,r.get?r:{enumerable:!0,get:()=>a[l]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))a(l);new MutationObserver(l=>{for(const r of l)if(r.type==="childList")for(const i of r.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&a(i)}).observe(document,{childList:!0,subtree:!0});function n(l){const r={};return l.integrity&&(r.integrity=l.integrity),l.referrerPolicy&&(r.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?r.credentials="include":l.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function a(l){if(l.ep)return;l.ep=!0;const r=n(l);fetch(l.href,r)}})();function Ap(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Tp={exports:{}},Es={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var tx=Symbol.for("react.transitional.element"),nx=Symbol.for("react.fragment");function Np(e,t,n){var a=null;if(n!==void 0&&(a=""+n),t.key!==void 0&&(a=""+t.key),"key"in t){n={};for(var l in t)l!=="key"&&(n[l]=t[l])}else n=t;return t=n.ref,{$$typeof:tx,type:e,key:a,ref:t!==void 0?t:null,props:n}}Es.Fragment=nx;Es.jsx=Np;Es.jsxs=Np;Tp.exports=Es;var c=Tp.exports,Cp={exports:{}},As={},jp={exports:{}},Rp={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(R,U){var _=R.length;R.push(U);e:for(;0<_;){var z=_-1>>>1,k=R[z];if(0<l(k,U))R[z]=U,R[_]=k,_=z;else break e}}function n(R){return R.length===0?null:R[0]}function a(R){if(R.length===0)return null;var U=R[0],_=R.pop();if(_!==U){R[0]=_;e:for(var z=0,k=R.length,Ee=k>>>1;z<Ee;){var K=2*(z+1)-1,I=R[K],J=K+1,Re=R[J];if(0>l(I,_))J<k&&0>l(Re,I)?(R[z]=Re,R[J]=_,z=J):(R[z]=I,R[K]=_,z=K);else if(J<k&&0>l(Re,_))R[z]=Re,R[J]=_,z=J;else break e}}return U}function l(R,U){var _=R.sortIndex-U.sortIndex;return _!==0?_:R.id-U.id}if(e.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var r=performance;e.unstable_now=function(){return r.now()}}else{var i=Date,o=i.now();e.unstable_now=function(){return i.now()-o}}var s=[],u=[],d=1,m=null,h=3,f=!1,w=!1,y=!1,x=!1,v=typeof setTimeout=="function"?setTimeout:null,g=typeof clearTimeout=="function"?clearTimeout:null,b=typeof setImmediate<"u"?setImmediate:null;function S(R){for(var U=n(u);U!==null;){if(U.callback===null)a(u);else if(U.startTime<=R)a(u),U.sortIndex=U.expirationTime,t(s,U);else break;U=n(u)}}function E(R){if(y=!1,S(R),!w)if(n(s)!==null)w=!0,T||(T=!0,L());else{var U=n(u);U!==null&&ce(E,U.startTime-R)}}var T=!1,A=-1,C=5,O=-1;function M(){return x?!0:!(e.unstable_now()-O<C)}function B(){if(x=!1,T){var R=e.unstable_now();O=R;var U=!0;try{e:{w=!1,y&&(y=!1,g(A),A=-1),f=!0;var _=h;try{t:{for(S(R),m=n(s);m!==null&&!(m.expirationTime>R&&M());){var z=m.callback;if(typeof z=="function"){m.callback=null,h=m.priorityLevel;var k=z(m.expirationTime<=R);if(R=e.unstable_now(),typeof k=="function"){m.callback=k,S(R),U=!0;break t}m===n(s)&&a(s),S(R)}else a(s);m=n(s)}if(m!==null)U=!0;else{var Ee=n(u);Ee!==null&&ce(E,Ee.startTime-R),U=!1}}break e}finally{m=null,h=_,f=!1}U=void 0}}finally{U?L():T=!1}}}var L;if(typeof b=="function")L=function(){b(B)};else if(typeof MessageChannel<"u"){var $=new MessageChannel,Z=$.port2;$.port1.onmessage=B,L=function(){Z.postMessage(null)}}else L=function(){v(B,0)};function ce(R,U){A=v(function(){R(e.unstable_now())},U)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(R){R.callback=null},e.unstable_forceFrameRate=function(R){0>R||125<R?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<R?Math.floor(1e3/R):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_next=function(R){switch(h){case 1:case 2:case 3:var U=3;break;default:U=h}var _=h;h=U;try{return R()}finally{h=_}},e.unstable_requestPaint=function(){x=!0},e.unstable_runWithPriority=function(R,U){switch(R){case 1:case 2:case 3:case 4:case 5:break;default:R=3}var _=h;h=R;try{return U()}finally{h=_}},e.unstable_scheduleCallback=function(R,U,_){var z=e.unstable_now();switch(typeof _=="object"&&_!==null?(_=_.delay,_=typeof _=="number"&&0<_?z+_:z):_=z,R){case 1:var k=-1;break;case 2:k=250;break;case 5:k=**********;break;case 4:k=1e4;break;default:k=5e3}return k=_+k,R={id:d++,callback:U,priorityLevel:R,startTime:_,expirationTime:k,sortIndex:-1},_>z?(R.sortIndex=_,t(u,R),n(s)===null&&R===n(u)&&(y?(g(A),A=-1):y=!0,ce(E,_-z))):(R.sortIndex=k,t(s,R),w||f||(w=!0,T||(T=!0,L()))),R},e.unstable_shouldYield=M,e.unstable_wrapCallback=function(R){var U=h;return function(){var _=h;h=U;try{return R.apply(this,arguments)}finally{h=_}}}})(Rp);jp.exports=Rp;var ax=jp.exports,Op={exports:{}},V={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var pf=Symbol.for("react.transitional.element"),lx=Symbol.for("react.portal"),rx=Symbol.for("react.fragment"),ix=Symbol.for("react.strict_mode"),ox=Symbol.for("react.profiler"),sx=Symbol.for("react.consumer"),cx=Symbol.for("react.context"),ux=Symbol.for("react.forward_ref"),fx=Symbol.for("react.suspense"),dx=Symbol.for("react.memo"),Mp=Symbol.for("react.lazy"),Kd=Symbol.iterator;function hx(e){return e===null||typeof e!="object"?null:(e=Kd&&e[Kd]||e["@@iterator"],typeof e=="function"?e:null)}var Dp={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},_p=Object.assign,zp={};function hr(e,t,n){this.props=e,this.context=t,this.refs=zp,this.updater=n||Dp}hr.prototype.isReactComponent={};hr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};hr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Up(){}Up.prototype=hr.prototype;function vf(e,t,n){this.props=e,this.context=t,this.refs=zp,this.updater=n||Dp}var gf=vf.prototype=new Up;gf.constructor=vf;_p(gf,hr.prototype);gf.isPureReactComponent=!0;var Fd=Array.isArray,Se={H:null,A:null,T:null,S:null,V:null},Lp=Object.prototype.hasOwnProperty;function yf(e,t,n,a,l,r){return n=r.ref,{$$typeof:pf,type:e,key:t,ref:n!==void 0?n:null,props:r}}function mx(e,t){return yf(e.type,t,void 0,void 0,void 0,e.props)}function bf(e){return typeof e=="object"&&e!==null&&e.$$typeof===pf}function px(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Jd=/\/+/g;function cc(e,t){return typeof e=="object"&&e!==null&&e.key!=null?px(""+e.key):t.toString(36)}function $d(){}function vx(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch(typeof e.status=="string"?e.then($d,$d):(e.status="pending",e.then(function(t){e.status==="pending"&&(e.status="fulfilled",e.value=t)},function(t){e.status==="pending"&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}function gl(e,t,n,a,l){var r=typeof e;(r==="undefined"||r==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(r){case"bigint":case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case pf:case lx:i=!0;break;case Mp:return i=e._init,gl(i(e._payload),t,n,a,l)}}if(i)return l=l(e),i=a===""?"."+cc(e,0):a,Fd(l)?(n="",i!=null&&(n=i.replace(Jd,"$&/")+"/"),gl(l,t,n,"",function(u){return u})):l!=null&&(bf(l)&&(l=mx(l,n+(l.key==null||e&&e.key===l.key?"":(""+l.key).replace(Jd,"$&/")+"/")+i)),t.push(l)),1;i=0;var o=a===""?".":a+":";if(Fd(e))for(var s=0;s<e.length;s++)a=e[s],r=o+cc(a,s),i+=gl(a,t,n,r,l);else if(s=hx(e),typeof s=="function")for(e=s.call(e),s=0;!(a=e.next()).done;)a=a.value,r=o+cc(a,s++),i+=gl(a,t,n,r,l);else if(r==="object"){if(typeof e.then=="function")return gl(vx(e),t,n,a,l);throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return i}function Ki(e,t,n){if(e==null)return e;var a=[],l=0;return gl(e,a,"","",function(r){return t.call(n,r,l++)}),a}function gx(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Wd=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function yx(){}V.Children={map:Ki,forEach:function(e,t,n){Ki(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Ki(e,function(){t++}),t},toArray:function(e){return Ki(e,function(t){return t})||[]},only:function(e){if(!bf(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};V.Component=hr;V.Fragment=rx;V.Profiler=ox;V.PureComponent=vf;V.StrictMode=ix;V.Suspense=fx;V.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=Se;V.__COMPILER_RUNTIME={__proto__:null,c:function(e){return Se.H.useMemoCache(e)}};V.cache=function(e){return function(){return e.apply(null,arguments)}};V.cloneElement=function(e,t,n){if(e==null)throw Error("The argument must be a React element, but you passed "+e+".");var a=_p({},e.props),l=e.key,r=void 0;if(t!=null)for(i in t.ref!==void 0&&(r=void 0),t.key!==void 0&&(l=""+t.key),t)!Lp.call(t,i)||i==="key"||i==="__self"||i==="__source"||i==="ref"&&t.ref===void 0||(a[i]=t[i]);var i=arguments.length-2;if(i===1)a.children=n;else if(1<i){for(var o=Array(i),s=0;s<i;s++)o[s]=arguments[s+2];a.children=o}return yf(e.type,l,void 0,void 0,r,a)};V.createContext=function(e){return e={$$typeof:cx,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null},e.Provider=e,e.Consumer={$$typeof:sx,_context:e},e};V.createElement=function(e,t,n){var a,l={},r=null;if(t!=null)for(a in t.key!==void 0&&(r=""+t.key),t)Lp.call(t,a)&&a!=="key"&&a!=="__self"&&a!=="__source"&&(l[a]=t[a]);var i=arguments.length-2;if(i===1)l.children=n;else if(1<i){for(var o=Array(i),s=0;s<i;s++)o[s]=arguments[s+2];l.children=o}if(e&&e.defaultProps)for(a in i=e.defaultProps,i)l[a]===void 0&&(l[a]=i[a]);return yf(e,r,void 0,void 0,null,l)};V.createRef=function(){return{current:null}};V.forwardRef=function(e){return{$$typeof:ux,render:e}};V.isValidElement=bf;V.lazy=function(e){return{$$typeof:Mp,_payload:{_status:-1,_result:e},_init:gx}};V.memo=function(e,t){return{$$typeof:dx,type:e,compare:t===void 0?null:t}};V.startTransition=function(e){var t=Se.T,n={};Se.T=n;try{var a=e(),l=Se.S;l!==null&&l(n,a),typeof a=="object"&&a!==null&&typeof a.then=="function"&&a.then(yx,Wd)}catch(r){Wd(r)}finally{Se.T=t}};V.unstable_useCacheRefresh=function(){return Se.H.useCacheRefresh()};V.use=function(e){return Se.H.use(e)};V.useActionState=function(e,t,n){return Se.H.useActionState(e,t,n)};V.useCallback=function(e,t){return Se.H.useCallback(e,t)};V.useContext=function(e){return Se.H.useContext(e)};V.useDebugValue=function(){};V.useDeferredValue=function(e,t){return Se.H.useDeferredValue(e,t)};V.useEffect=function(e,t,n){var a=Se.H;if(typeof n=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return a.useEffect(e,t)};V.useId=function(){return Se.H.useId()};V.useImperativeHandle=function(e,t,n){return Se.H.useImperativeHandle(e,t,n)};V.useInsertionEffect=function(e,t){return Se.H.useInsertionEffect(e,t)};V.useLayoutEffect=function(e,t){return Se.H.useLayoutEffect(e,t)};V.useMemo=function(e,t){return Se.H.useMemo(e,t)};V.useOptimistic=function(e,t){return Se.H.useOptimistic(e,t)};V.useReducer=function(e,t,n){return Se.H.useReducer(e,t,n)};V.useRef=function(e){return Se.H.useRef(e)};V.useState=function(e){return Se.H.useState(e)};V.useSyncExternalStore=function(e,t,n){return Se.H.useSyncExternalStore(e,t,n)};V.useTransition=function(){return Se.H.useTransition()};V.version="19.1.1";Op.exports=V;var p=Op.exports;const D=Ap(p),xf=ex({__proto__:null,default:D},[p]);var Bp={exports:{}},tt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bx=p;function Hp(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function Qn(){}var et={d:{f:Qn,r:function(){throw Error(Hp(522))},D:Qn,C:Qn,L:Qn,m:Qn,X:Qn,S:Qn,M:Qn},p:0,findDOMNode:null},xx=Symbol.for("react.portal");function Sx(e,t,n){var a=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:xx,key:a==null?null:""+a,children:e,containerInfo:t,implementation:n}}var Gr=bx.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function Ts(e,t){if(e==="font")return"";if(typeof t=="string")return t==="use-credentials"?t:""}tt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=et;tt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)throw Error(Hp(299));return Sx(e,t,null,n)};tt.flushSync=function(e){var t=Gr.T,n=et.p;try{if(Gr.T=null,et.p=2,e)return e()}finally{Gr.T=t,et.p=n,et.d.f()}};tt.preconnect=function(e,t){typeof e=="string"&&(t?(t=t.crossOrigin,t=typeof t=="string"?t==="use-credentials"?t:"":void 0):t=null,et.d.C(e,t))};tt.prefetchDNS=function(e){typeof e=="string"&&et.d.D(e)};tt.preinit=function(e,t){if(typeof e=="string"&&t&&typeof t.as=="string"){var n=t.as,a=Ts(n,t.crossOrigin),l=typeof t.integrity=="string"?t.integrity:void 0,r=typeof t.fetchPriority=="string"?t.fetchPriority:void 0;n==="style"?et.d.S(e,typeof t.precedence=="string"?t.precedence:void 0,{crossOrigin:a,integrity:l,fetchPriority:r}):n==="script"&&et.d.X(e,{crossOrigin:a,integrity:l,fetchPriority:r,nonce:typeof t.nonce=="string"?t.nonce:void 0})}};tt.preinitModule=function(e,t){if(typeof e=="string")if(typeof t=="object"&&t!==null){if(t.as==null||t.as==="script"){var n=Ts(t.as,t.crossOrigin);et.d.M(e,{crossOrigin:n,integrity:typeof t.integrity=="string"?t.integrity:void 0,nonce:typeof t.nonce=="string"?t.nonce:void 0})}}else t==null&&et.d.M(e)};tt.preload=function(e,t){if(typeof e=="string"&&typeof t=="object"&&t!==null&&typeof t.as=="string"){var n=t.as,a=Ts(n,t.crossOrigin);et.d.L(e,n,{crossOrigin:a,integrity:typeof t.integrity=="string"?t.integrity:void 0,nonce:typeof t.nonce=="string"?t.nonce:void 0,type:typeof t.type=="string"?t.type:void 0,fetchPriority:typeof t.fetchPriority=="string"?t.fetchPriority:void 0,referrerPolicy:typeof t.referrerPolicy=="string"?t.referrerPolicy:void 0,imageSrcSet:typeof t.imageSrcSet=="string"?t.imageSrcSet:void 0,imageSizes:typeof t.imageSizes=="string"?t.imageSizes:void 0,media:typeof t.media=="string"?t.media:void 0})}};tt.preloadModule=function(e,t){if(typeof e=="string")if(t){var n=Ts(t.as,t.crossOrigin);et.d.m(e,{as:typeof t.as=="string"&&t.as!=="script"?t.as:void 0,crossOrigin:n,integrity:typeof t.integrity=="string"?t.integrity:void 0})}else et.d.m(e)};tt.requestFormReset=function(e){et.d.r(e)};tt.unstable_batchedUpdates=function(e,t){return e(t)};tt.useFormState=function(e,t,n){return Gr.H.useFormState(e,t,n)};tt.useFormStatus=function(){return Gr.H.useHostTransitionStatus()};tt.version="19.1.1";function kp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(kp)}catch(e){console.error(e)}}kp(),Bp.exports=tt;var Ns=Bp.exports;const qp=Ap(Ns);/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ke=ax,Gp=p,wx=Ns;function j(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function Yp(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ni(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Vp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Id(e){if(Ni(e)!==e)throw Error(j(188))}function Ex(e){var t=e.alternate;if(!t){if(t=Ni(e),t===null)throw Error(j(188));return t!==e?null:e}for(var n=e,a=t;;){var l=n.return;if(l===null)break;var r=l.alternate;if(r===null){if(a=l.return,a!==null){n=a;continue}break}if(l.child===r.child){for(r=l.child;r;){if(r===n)return Id(l),e;if(r===a)return Id(l),t;r=r.sibling}throw Error(j(188))}if(n.return!==a.return)n=l,a=r;else{for(var i=!1,o=l.child;o;){if(o===n){i=!0,n=l,a=r;break}if(o===a){i=!0,a=l,n=r;break}o=o.sibling}if(!i){for(o=r.child;o;){if(o===n){i=!0,n=r,a=l;break}if(o===a){i=!0,a=r,n=l;break}o=o.sibling}if(!i)throw Error(j(189))}}if(n.alternate!==a)throw Error(j(190))}if(n.tag!==3)throw Error(j(188));return n.stateNode.current===n?e:t}function Qp(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=Qp(e),t!==null)return t;e=e.sibling}return null}var me=Object.assign,Ax=Symbol.for("react.element"),Fi=Symbol.for("react.transitional.element"),Ur=Symbol.for("react.portal"),Sl=Symbol.for("react.fragment"),Xp=Symbol.for("react.strict_mode"),lu=Symbol.for("react.profiler"),Tx=Symbol.for("react.provider"),Pp=Symbol.for("react.consumer"),wn=Symbol.for("react.context"),Sf=Symbol.for("react.forward_ref"),ru=Symbol.for("react.suspense"),iu=Symbol.for("react.suspense_list"),wf=Symbol.for("react.memo"),$n=Symbol.for("react.lazy"),ou=Symbol.for("react.activity"),Nx=Symbol.for("react.memo_cache_sentinel"),eh=Symbol.iterator;function Tr(e){return e===null||typeof e!="object"?null:(e=eh&&e[eh]||e["@@iterator"],typeof e=="function"?e:null)}var Cx=Symbol.for("react.client.reference");function su(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Cx?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Sl:return"Fragment";case lu:return"Profiler";case Xp:return"StrictMode";case ru:return"Suspense";case iu:return"SuspenseList";case ou:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case Ur:return"Portal";case wn:return(e.displayName||"Context")+".Provider";case Pp:return(e._context.displayName||"Context")+".Consumer";case Sf:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case wf:return t=e.displayName||null,t!==null?t:su(e.type)||"Memo";case $n:t=e._payload,e=e._init;try{return su(e(t))}catch{}}return null}var Lr=Array.isArray,H=Gp.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,te=wx.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Ya={pending:!1,data:null,method:null,action:null},cu=[],wl=-1;function sn(e){return{current:e}}function Ve(e){0>wl||(e.current=cu[wl],cu[wl]=null,wl--)}function we(e,t){wl++,cu[wl]=e.current,e.current=t}var nn=sn(null),ai=sn(null),ha=sn(null),Lo=sn(null);function Bo(e,t){switch(we(ha,t),we(ai,e),we(nn,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?rm(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=rm(t),e=cy(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}Ve(nn),we(nn,e)}function Il(){Ve(nn),Ve(ai),Ve(ha)}function uu(e){e.memoizedState!==null&&we(Lo,e);var t=nn.current,n=cy(t,e.type);t!==n&&(we(ai,e),we(nn,n))}function Ho(e){ai.current===e&&(Ve(nn),Ve(ai)),Lo.current===e&&(Ve(Lo),hi._currentValue=Ya)}var fu=Object.prototype.hasOwnProperty,Ef=ke.unstable_scheduleCallback,uc=ke.unstable_cancelCallback,jx=ke.unstable_shouldYield,Rx=ke.unstable_requestPaint,an=ke.unstable_now,Ox=ke.unstable_getCurrentPriorityLevel,Zp=ke.unstable_ImmediatePriority,Kp=ke.unstable_UserBlockingPriority,ko=ke.unstable_NormalPriority,Mx=ke.unstable_LowPriority,Fp=ke.unstable_IdlePriority,Dx=ke.log,_x=ke.unstable_setDisableYieldValue,Ci=null,pt=null;function sa(e){if(typeof Dx=="function"&&_x(e),pt&&typeof pt.setStrictMode=="function")try{pt.setStrictMode(Ci,e)}catch{}}var vt=Math.clz32?Math.clz32:Lx,zx=Math.log,Ux=Math.LN2;function Lx(e){return e>>>=0,e===0?32:31-(zx(e)/Ux|0)|0}var Ji=256,$i=4194304;function Da(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Cs(e,t,n){var a=e.pendingLanes;if(a===0)return 0;var l=0,r=e.suspendedLanes,i=e.pingedLanes;e=e.warmLanes;var o=a&134217727;return o!==0?(a=o&~r,a!==0?l=Da(a):(i&=o,i!==0?l=Da(i):n||(n=o&~e,n!==0&&(l=Da(n))))):(o=a&~r,o!==0?l=Da(o):i!==0?l=Da(i):n||(n=a&~e,n!==0&&(l=Da(n)))),l===0?0:t!==0&&t!==l&&!(t&r)&&(r=l&-l,n=t&-t,r>=n||r===32&&(n&4194048)!==0)?t:l}function ji(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Bx(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Jp(){var e=Ji;return Ji<<=1,!(Ji&4194048)&&(Ji=256),e}function $p(){var e=$i;return $i<<=1,!($i&62914560)&&($i=4194304),e}function fc(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ri(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Hx(e,t,n,a,l,r){var i=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var o=e.entanglements,s=e.expirationTimes,u=e.hiddenUpdates;for(n=i&~n;0<n;){var d=31-vt(n),m=1<<d;o[d]=0,s[d]=-1;var h=u[d];if(h!==null)for(u[d]=null,d=0;d<h.length;d++){var f=h[d];f!==null&&(f.lane&=-536870913)}n&=~m}a!==0&&Wp(e,a,0),r!==0&&l===0&&e.tag!==0&&(e.suspendedLanes|=r&~(i&~t))}function Wp(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-vt(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|n&4194090}function Ip(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var a=31-vt(n),l=1<<a;l&t|e[a]&t&&(e[a]|=t),n&=~l}}function Af(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Tf(e){return e&=-e,2<e?8<e?e&134217727?32:268435456:8:2}function ev(){var e=te.p;return e!==0?e:(e=window.event,e===void 0?32:by(e.type))}function kx(e,t){var n=te.p;try{return te.p=e,t()}finally{te.p=n}}var Na=Math.random().toString(36).slice(2),Fe="__reactFiber$"+Na,ot="__reactProps$"+Na,mr="__reactContainer$"+Na,du="__reactEvents$"+Na,qx="__reactListeners$"+Na,Gx="__reactHandles$"+Na,th="__reactResources$"+Na,Oi="__reactMarker$"+Na;function Nf(e){delete e[Fe],delete e[ot],delete e[du],delete e[qx],delete e[Gx]}function El(e){var t=e[Fe];if(t)return t;for(var n=e.parentNode;n;){if(t=n[mr]||n[Fe]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=sm(e);e!==null;){if(n=e[Fe])return n;e=sm(e)}return t}e=n,n=e.parentNode}return null}function pr(e){if(e=e[Fe]||e[mr]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Br(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(j(33))}function _l(e){var t=e[th];return t||(t=e[th]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ge(e){e[Oi]=!0}var tv=new Set,nv={};function tl(e,t){er(e,t),er(e+"Capture",t)}function er(e,t){for(nv[e]=t,e=0;e<t.length;e++)tv.add(t[e])}var Yx=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),nh={},ah={};function Vx(e){return fu.call(ah,e)?!0:fu.call(nh,e)?!1:Yx.test(e)?ah[e]=!0:(nh[e]=!0,!1)}function yo(e,t,n){if(Vx(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function Wi(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function hn(e,t,n,a){if(a===null)e.removeAttribute(n);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+a)}}var dc,lh;function yl(e){if(dc===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);dc=t&&t[1]||"",lh=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+dc+e+lh}var hc=!1;function mc(e,t){if(!e||hc)return"";hc=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var m=function(){throw Error()};if(Object.defineProperty(m.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(m,[])}catch(f){var h=f}Reflect.construct(e,[],m)}else{try{m.call()}catch(f){h=f}e.call(m.prototype)}}else{try{throw Error()}catch(f){h=f}(m=e())&&typeof m.catch=="function"&&m.catch(function(){})}}catch(f){if(f&&h&&typeof f.stack=="string")return[f.stack,h.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var l=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");l&&l.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var r=a.DetermineComponentFrameRoot(),i=r[0],o=r[1];if(i&&o){var s=i.split(`
`),u=o.split(`
`);for(l=a=0;a<s.length&&!s[a].includes("DetermineComponentFrameRoot");)a++;for(;l<u.length&&!u[l].includes("DetermineComponentFrameRoot");)l++;if(a===s.length||l===u.length)for(a=s.length-1,l=u.length-1;1<=a&&0<=l&&s[a]!==u[l];)l--;for(;1<=a&&0<=l;a--,l--)if(s[a]!==u[l]){if(a!==1||l!==1)do if(a--,l--,0>l||s[a]!==u[l]){var d=`
`+s[a].replace(" at new "," at ");return e.displayName&&d.includes("<anonymous>")&&(d=d.replace("<anonymous>",e.displayName)),d}while(1<=a&&0<=l);break}}}finally{hc=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?yl(n):""}function Qx(e){switch(e.tag){case 26:case 27:case 5:return yl(e.type);case 16:return yl("Lazy");case 13:return yl("Suspense");case 19:return yl("SuspenseList");case 0:case 15:return mc(e.type,!1);case 11:return mc(e.type.render,!1);case 1:return mc(e.type,!0);case 31:return yl("Activity");default:return""}}function rh(e){try{var t="";do t+=Qx(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Tt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function av(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Xx(e){var t=av(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,r=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(i){a=""+i,r.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return a},setValue:function(i){a=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function qo(e){e._valueTracker||(e._valueTracker=Xx(e))}function lv(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),a="";return e&&(a=av(e)?e.checked?"true":"false":e.value),e=a,e!==n?(t.setValue(e),!0):!1}function Go(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Px=/[\n"\\]/g;function jt(e){return e.replace(Px,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function hu(e,t,n,a,l,r,i,o){e.name="",i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"?e.type=i:e.removeAttribute("type"),t!=null?i==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Tt(t)):e.value!==""+Tt(t)&&(e.value=""+Tt(t)):i!=="submit"&&i!=="reset"||e.removeAttribute("value"),t!=null?mu(e,i,Tt(t)):n!=null?mu(e,i,Tt(n)):a!=null&&e.removeAttribute("value"),l==null&&r!=null&&(e.defaultChecked=!!r),l!=null&&(e.checked=l&&typeof l!="function"&&typeof l!="symbol"),o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?e.name=""+Tt(o):e.removeAttribute("name")}function rv(e,t,n,a,l,r,i,o){if(r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(e.type=r),t!=null||n!=null){if(!(r!=="submit"&&r!=="reset"||t!=null))return;n=n!=null?""+Tt(n):"",t=t!=null?""+Tt(t):n,o||t===e.value||(e.value=t),e.defaultValue=t}a=a??l,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=o?e.checked:!!a,e.defaultChecked=!!a,i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(e.name=i)}function mu(e,t,n){t==="number"&&Go(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function zl(e,t,n,a){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&a&&(e[n].defaultSelected=!0)}else{for(n=""+Tt(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,a&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function iv(e,t,n){if(t!=null&&(t=""+Tt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+Tt(n):""}function ov(e,t,n,a){if(t==null){if(a!=null){if(n!=null)throw Error(j(92));if(Lr(a)){if(1<a.length)throw Error(j(93));a=a[0]}n=a}n==null&&(n=""),t=n}n=Tt(t),e.defaultValue=n,a=e.textContent,a===n&&a!==""&&a!==null&&(e.value=a)}function tr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Zx=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function ih(e,t,n){var a=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,n):typeof n!="number"||n===0||Zx.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function sv(e,t,n){if(t!=null&&typeof t!="object")throw Error(j(62));if(e=e.style,n!=null){for(var a in n)!n.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var l in t)a=t[l],t.hasOwnProperty(l)&&n[l]!==a&&ih(e,l,a)}else for(var r in t)t.hasOwnProperty(r)&&ih(e,r,t[r])}function Cf(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Kx=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Fx=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function bo(e){return Fx.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var pu=null;function jf(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Al=null,Ul=null;function oh(e){var t=pr(e);if(t&&(e=t.stateNode)){var n=e[ot]||null;e:switch(e=t.stateNode,t.type){case"input":if(hu(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+jt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var a=n[t];if(a!==e&&a.form===e.form){var l=a[ot]||null;if(!l)throw Error(j(90));hu(a,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name)}}for(t=0;t<n.length;t++)a=n[t],a.form===e.form&&lv(a)}break e;case"textarea":iv(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&zl(e,!!n.multiple,t,!1)}}}var pc=!1;function cv(e,t,n){if(pc)return e(t,n);pc=!0;try{var a=e(t);return a}finally{if(pc=!1,(Al!==null||Ul!==null)&&(Bs(),Al&&(t=Al,e=Ul,Ul=Al=null,oh(t),e)))for(t=0;t<e.length;t++)oh(e[t])}}function li(e,t){var n=e.stateNode;if(n===null)return null;var a=n[ot]||null;if(a===null)return null;n=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(j(231,t,typeof n));return n}var Rn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),vu=!1;if(Rn)try{var Nr={};Object.defineProperty(Nr,"passive",{get:function(){vu=!0}}),window.addEventListener("test",Nr,Nr),window.removeEventListener("test",Nr,Nr)}catch{vu=!1}var ca=null,Rf=null,xo=null;function uv(){if(xo)return xo;var e,t=Rf,n=t.length,a,l="value"in ca?ca.value:ca.textContent,r=l.length;for(e=0;e<n&&t[e]===l[e];e++);var i=n-e;for(a=1;a<=i&&t[n-a]===l[r-a];a++);return xo=l.slice(e,1<a?1-a:void 0)}function So(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ii(){return!0}function sh(){return!1}function ct(e){function t(n,a,l,r,i){this._reactName=n,this._targetInst=l,this.type=a,this.nativeEvent=r,this.target=i,this.currentTarget=null;for(var o in e)e.hasOwnProperty(o)&&(n=e[o],this[o]=n?n(r):r[o]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?Ii:sh,this.isPropagationStopped=sh,this}return me(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ii)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ii)},persist:function(){},isPersistent:Ii}),t}var nl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},js=ct(nl),Mi=me({},nl,{view:0,detail:0}),Jx=ct(Mi),vc,gc,Cr,Rs=me({},Mi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Of,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Cr&&(Cr&&e.type==="mousemove"?(vc=e.screenX-Cr.screenX,gc=e.screenY-Cr.screenY):gc=vc=0,Cr=e),vc)},movementY:function(e){return"movementY"in e?e.movementY:gc}}),ch=ct(Rs),$x=me({},Rs,{dataTransfer:0}),Wx=ct($x),Ix=me({},Mi,{relatedTarget:0}),yc=ct(Ix),e1=me({},nl,{animationName:0,elapsedTime:0,pseudoElement:0}),t1=ct(e1),n1=me({},nl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),a1=ct(n1),l1=me({},nl,{data:0}),uh=ct(l1),r1={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},i1={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},o1={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function s1(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=o1[e])?!!t[e]:!1}function Of(){return s1}var c1=me({},Mi,{key:function(e){if(e.key){var t=r1[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=So(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?i1[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Of,charCode:function(e){return e.type==="keypress"?So(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?So(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),u1=ct(c1),f1=me({},Rs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),fh=ct(f1),d1=me({},Mi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Of}),h1=ct(d1),m1=me({},nl,{propertyName:0,elapsedTime:0,pseudoElement:0}),p1=ct(m1),v1=me({},Rs,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),g1=ct(v1),y1=me({},nl,{newState:0,oldState:0}),b1=ct(y1),x1=[9,13,27,32],Mf=Rn&&"CompositionEvent"in window,Yr=null;Rn&&"documentMode"in document&&(Yr=document.documentMode);var S1=Rn&&"TextEvent"in window&&!Yr,fv=Rn&&(!Mf||Yr&&8<Yr&&11>=Yr),dh=" ",hh=!1;function dv(e,t){switch(e){case"keyup":return x1.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function hv(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Tl=!1;function w1(e,t){switch(e){case"compositionend":return hv(t);case"keypress":return t.which!==32?null:(hh=!0,dh);case"textInput":return e=t.data,e===dh&&hh?null:e;default:return null}}function E1(e,t){if(Tl)return e==="compositionend"||!Mf&&dv(e,t)?(e=uv(),xo=Rf=ca=null,Tl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return fv&&t.locale!=="ko"?null:t.data;default:return null}}var A1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function mh(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!A1[e.type]:t==="textarea"}function mv(e,t,n,a){Al?Ul?Ul.push(a):Ul=[a]:Al=a,t=rs(t,"onChange"),0<t.length&&(n=new js("onChange","change",null,n,a),e.push({event:n,listeners:t}))}var Vr=null,ri=null;function T1(e){iy(e,0)}function Os(e){var t=Br(e);if(lv(t))return e}function ph(e,t){if(e==="change")return t}var pv=!1;if(Rn){var bc;if(Rn){var xc="oninput"in document;if(!xc){var vh=document.createElement("div");vh.setAttribute("oninput","return;"),xc=typeof vh.oninput=="function"}bc=xc}else bc=!1;pv=bc&&(!document.documentMode||9<document.documentMode)}function gh(){Vr&&(Vr.detachEvent("onpropertychange",vv),ri=Vr=null)}function vv(e){if(e.propertyName==="value"&&Os(ri)){var t=[];mv(t,ri,e,jf(e)),cv(T1,t)}}function N1(e,t,n){e==="focusin"?(gh(),Vr=t,ri=n,Vr.attachEvent("onpropertychange",vv)):e==="focusout"&&gh()}function C1(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Os(ri)}function j1(e,t){if(e==="click")return Os(t)}function R1(e,t){if(e==="input"||e==="change")return Os(t)}function O1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var xt=typeof Object.is=="function"?Object.is:O1;function ii(e,t){if(xt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),a=Object.keys(t);if(n.length!==a.length)return!1;for(a=0;a<n.length;a++){var l=n[a];if(!fu.call(t,l)||!xt(e[l],t[l]))return!1}return!0}function yh(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function bh(e,t){var n=yh(e);e=0;for(var a;n;){if(n.nodeType===3){if(a=e+n.textContent.length,e<=t&&a>=t)return{node:n,offset:t-e};e=a}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=yh(n)}}function gv(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?gv(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function yv(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Go(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Go(e.document)}return t}function Df(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var M1=Rn&&"documentMode"in document&&11>=document.documentMode,Nl=null,gu=null,Qr=null,yu=!1;function xh(e,t,n){var a=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;yu||Nl==null||Nl!==Go(a)||(a=Nl,"selectionStart"in a&&Df(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),Qr&&ii(Qr,a)||(Qr=a,a=rs(gu,"onSelect"),0<a.length&&(t=new js("onSelect","select",null,t,n),e.push({event:t,listeners:a}),t.target=Nl)))}function Ma(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Cl={animationend:Ma("Animation","AnimationEnd"),animationiteration:Ma("Animation","AnimationIteration"),animationstart:Ma("Animation","AnimationStart"),transitionrun:Ma("Transition","TransitionRun"),transitionstart:Ma("Transition","TransitionStart"),transitioncancel:Ma("Transition","TransitionCancel"),transitionend:Ma("Transition","TransitionEnd")},Sc={},bv={};Rn&&(bv=document.createElement("div").style,"AnimationEvent"in window||(delete Cl.animationend.animation,delete Cl.animationiteration.animation,delete Cl.animationstart.animation),"TransitionEvent"in window||delete Cl.transitionend.transition);function al(e){if(Sc[e])return Sc[e];if(!Cl[e])return e;var t=Cl[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in bv)return Sc[e]=t[n];return e}var xv=al("animationend"),Sv=al("animationiteration"),wv=al("animationstart"),D1=al("transitionrun"),_1=al("transitionstart"),z1=al("transitioncancel"),Ev=al("transitionend"),Av=new Map,bu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");bu.push("scrollEnd");function Xt(e,t){Av.set(e,t),tl(t,[e])}var Sh=new WeakMap;function Rt(e,t){if(typeof e=="object"&&e!==null){var n=Sh.get(e);return n!==void 0?n:(t={value:e,source:t,stack:rh(t)},Sh.set(e,t),t)}return{value:e,source:t,stack:rh(t)}}var Et=[],jl=0,_f=0;function Ms(){for(var e=jl,t=_f=jl=0;t<e;){var n=Et[t];Et[t++]=null;var a=Et[t];Et[t++]=null;var l=Et[t];Et[t++]=null;var r=Et[t];if(Et[t++]=null,a!==null&&l!==null){var i=a.pending;i===null?l.next=l:(l.next=i.next,i.next=l),a.pending=l}r!==0&&Tv(n,l,r)}}function Ds(e,t,n,a){Et[jl++]=e,Et[jl++]=t,Et[jl++]=n,Et[jl++]=a,_f|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function zf(e,t,n,a){return Ds(e,t,n,a),Yo(e)}function vr(e,t){return Ds(e,null,null,t),Yo(e)}function Tv(e,t,n){e.lanes|=n;var a=e.alternate;a!==null&&(a.lanes|=n);for(var l=!1,r=e.return;r!==null;)r.childLanes|=n,a=r.alternate,a!==null&&(a.childLanes|=n),r.tag===22&&(e=r.stateNode,e===null||e._visibility&1||(l=!0)),e=r,r=r.return;return e.tag===3?(r=e.stateNode,l&&t!==null&&(l=31-vt(n),e=r.hiddenUpdates,a=e[l],a===null?e[l]=[t]:a.push(t),t.lane=n|536870912),r):null}function Yo(e){if(50<ei)throw ei=0,qu=null,Error(j(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Rl={};function U1(e,t,n,a){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function mt(e,t,n,a){return new U1(e,t,n,a)}function Uf(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Cn(e,t){var n=e.alternate;return n===null?(n=mt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Nv(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function wo(e,t,n,a,l,r){var i=0;if(a=e,typeof e=="function")Uf(e)&&(i=1);else if(typeof e=="string")i=BS(e,n,nn.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ou:return e=mt(31,n,t,l),e.elementType=ou,e.lanes=r,e;case Sl:return Va(n.children,l,r,t);case Xp:i=8,l|=24;break;case lu:return e=mt(12,n,t,l|2),e.elementType=lu,e.lanes=r,e;case ru:return e=mt(13,n,t,l),e.elementType=ru,e.lanes=r,e;case iu:return e=mt(19,n,t,l),e.elementType=iu,e.lanes=r,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Tx:case wn:i=10;break e;case Pp:i=9;break e;case Sf:i=11;break e;case wf:i=14;break e;case $n:i=16,a=null;break e}i=29,n=Error(j(130,e===null?"null":typeof e,"")),a=null}return t=mt(i,n,t,l),t.elementType=e,t.type=a,t.lanes=r,t}function Va(e,t,n,a){return e=mt(7,e,a,t),e.lanes=n,e}function wc(e,t,n){return e=mt(6,e,null,t),e.lanes=n,e}function Ec(e,t,n){return t=mt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Ol=[],Ml=0,Vo=null,Qo=0,Nt=[],Ct=0,Qa=null,En=1,An="";function _a(e,t){Ol[Ml++]=Qo,Ol[Ml++]=Vo,Vo=e,Qo=t}function Cv(e,t,n){Nt[Ct++]=En,Nt[Ct++]=An,Nt[Ct++]=Qa,Qa=e;var a=En;e=An;var l=32-vt(a)-1;a&=~(1<<l),n+=1;var r=32-vt(t)+l;if(30<r){var i=l-l%5;r=(a&(1<<i)-1).toString(32),a>>=i,l-=i,En=1<<32-vt(t)+l|n<<l|a,An=r+e}else En=1<<r|n<<l|a,An=e}function Lf(e){e.return!==null&&(_a(e,1),Cv(e,1,0))}function Bf(e){for(;e===Vo;)Vo=Ol[--Ml],Ol[Ml]=null,Qo=Ol[--Ml],Ol[Ml]=null;for(;e===Qa;)Qa=Nt[--Ct],Nt[Ct]=null,An=Nt[--Ct],Nt[Ct]=null,En=Nt[--Ct],Nt[Ct]=null}var Ie=null,Ce=null,ee=!1,Xa=null,$t=!1,xu=Error(j(519));function Fa(e){var t=Error(j(418,""));throw oi(Rt(t,e)),xu}function wh(e){var t=e.stateNode,n=e.type,a=e.memoizedProps;switch(t[Fe]=e,t[ot]=a,n){case"dialog":X("cancel",t),X("close",t);break;case"iframe":case"object":case"embed":X("load",t);break;case"video":case"audio":for(n=0;n<ui.length;n++)X(ui[n],t);break;case"source":X("error",t);break;case"img":case"image":case"link":X("error",t),X("load",t);break;case"details":X("toggle",t);break;case"input":X("invalid",t),rv(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),qo(t);break;case"select":X("invalid",t);break;case"textarea":X("invalid",t),ov(t,a.value,a.defaultValue,a.children),qo(t)}n=a.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||a.suppressHydrationWarning===!0||sy(t.textContent,n)?(a.popover!=null&&(X("beforetoggle",t),X("toggle",t)),a.onScroll!=null&&X("scroll",t),a.onScrollEnd!=null&&X("scrollend",t),a.onClick!=null&&(t.onclick=qs),t=!0):t=!1,t||Fa(e)}function Eh(e){for(Ie=e.return;Ie;)switch(Ie.tag){case 5:case 13:$t=!1;return;case 27:case 3:$t=!0;return;default:Ie=Ie.return}}function jr(e){if(e!==Ie)return!1;if(!ee)return Eh(e),ee=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||Pu(e.type,e.memoizedProps)),n=!n),n&&Ce&&Fa(e),Eh(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(j(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){Ce=Yt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}Ce=null}}else t===27?(t=Ce,Ca(e.type)?(e=Fu,Fu=null,Ce=e):Ce=t):Ce=Ie?Yt(e.stateNode.nextSibling):null;return!0}function Di(){Ce=Ie=null,ee=!1}function Ah(){var e=Xa;return e!==null&&(lt===null?lt=e:lt.push.apply(lt,e),Xa=null),e}function oi(e){Xa===null?Xa=[e]:Xa.push(e)}var Su=sn(null),ll=null,Tn=null;function In(e,t,n){we(Su,t._currentValue),t._currentValue=n}function jn(e){e._currentValue=Su.current,Ve(Su)}function wu(e,t,n){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===n)break;e=e.return}}function Eu(e,t,n,a){var l=e.child;for(l!==null&&(l.return=e);l!==null;){var r=l.dependencies;if(r!==null){var i=l.child;r=r.firstContext;e:for(;r!==null;){var o=r;r=l;for(var s=0;s<t.length;s++)if(o.context===t[s]){r.lanes|=n,o=r.alternate,o!==null&&(o.lanes|=n),wu(r.return,n,e),a||(i=null);break e}r=o.next}}else if(l.tag===18){if(i=l.return,i===null)throw Error(j(341));i.lanes|=n,r=i.alternate,r!==null&&(r.lanes|=n),wu(i,n,e),i=null}else i=l.child;if(i!==null)i.return=l;else for(i=l;i!==null;){if(i===e){i=null;break}if(l=i.sibling,l!==null){l.return=i.return,i=l;break}i=i.return}l=i}}function _i(e,t,n,a){e=null;for(var l=t,r=!1;l!==null;){if(!r){if(l.flags&524288)r=!0;else if(l.flags&262144)break}if(l.tag===10){var i=l.alternate;if(i===null)throw Error(j(387));if(i=i.memoizedProps,i!==null){var o=l.type;xt(l.pendingProps.value,i.value)||(e!==null?e.push(o):e=[o])}}else if(l===Lo.current){if(i=l.alternate,i===null)throw Error(j(387));i.memoizedState.memoizedState!==l.memoizedState.memoizedState&&(e!==null?e.push(hi):e=[hi])}l=l.return}e!==null&&Eu(t,e,n,a),t.flags|=262144}function Xo(e){for(e=e.firstContext;e!==null;){if(!xt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ja(e){ll=e,Tn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Je(e){return jv(ll,e)}function eo(e,t){return ll===null&&Ja(e),jv(e,t)}function jv(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},Tn===null){if(e===null)throw Error(j(308));Tn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Tn=Tn.next=t;return n}var L1=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},B1=ke.unstable_scheduleCallback,H1=ke.unstable_NormalPriority,Be={$$typeof:wn,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Hf(){return{controller:new L1,data:new Map,refCount:0}}function zi(e){e.refCount--,e.refCount===0&&B1(H1,function(){e.controller.abort()})}var Xr=null,Au=0,nr=0,Ll=null;function k1(e,t){if(Xr===null){var n=Xr=[];Au=0,nr=od(),Ll={status:"pending",value:void 0,then:function(a){n.push(a)}}}return Au++,t.then(Th,Th),t}function Th(){if(--Au===0&&Xr!==null){Ll!==null&&(Ll.status="fulfilled");var e=Xr;Xr=null,nr=0,Ll=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function q1(e,t){var n=[],a={status:"pending",value:null,reason:null,then:function(l){n.push(l)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var l=0;l<n.length;l++)(0,n[l])(t)},function(l){for(a.status="rejected",a.reason=l,l=0;l<n.length;l++)(0,n[l])(void 0)}),a}var Nh=H.S;H.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&k1(e,t),Nh!==null&&Nh(e,t)};var Pa=sn(null);function kf(){var e=Pa.current;return e!==null?e:fe.pooledCache}function Eo(e,t){t===null?we(Pa,Pa.current):we(Pa,t.pool)}function Rv(){var e=kf();return e===null?null:{parent:Be._currentValue,pool:e}}var Ui=Error(j(460)),Ov=Error(j(474)),_s=Error(j(542)),Tu={then:function(){}};function Ch(e){return e=e.status,e==="fulfilled"||e==="rejected"}function to(){}function Mv(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(to,to),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Rh(e),e;default:if(typeof t.status=="string")t.then(to,to);else{if(e=fe,e!==null&&100<e.shellSuspendCounter)throw Error(j(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var l=t;l.status="fulfilled",l.value=a}},function(a){if(t.status==="pending"){var l=t;l.status="rejected",l.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Rh(e),e}throw Pr=t,Ui}}var Pr=null;function jh(){if(Pr===null)throw Error(j(459));var e=Pr;return Pr=null,e}function Rh(e){if(e===Ui||e===_s)throw Error(j(483))}var Wn=!1;function qf(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Nu(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ma(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function pa(e,t,n){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,re&2){var l=a.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),a.pending=t,t=Yo(e),Tv(e,null,n),t}return Ds(e,a,t,n),Yo(e)}function Zr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,n|=a,t.lanes=n,Ip(e,n)}}function Ac(e,t){var n=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,n===a)){var l=null,r=null;if(n=n.firstBaseUpdate,n!==null){do{var i={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};r===null?l=r=i:r=r.next=i,n=n.next}while(n!==null);r===null?l=r=t:r=r.next=t}else l=r=t;n={baseState:a.baseState,firstBaseUpdate:l,lastBaseUpdate:r,shared:a.shared,callbacks:a.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var Cu=!1;function Kr(){if(Cu){var e=Ll;if(e!==null)throw e}}function Fr(e,t,n,a){Cu=!1;var l=e.updateQueue;Wn=!1;var r=l.firstBaseUpdate,i=l.lastBaseUpdate,o=l.shared.pending;if(o!==null){l.shared.pending=null;var s=o,u=s.next;s.next=null,i===null?r=u:i.next=u,i=s;var d=e.alternate;d!==null&&(d=d.updateQueue,o=d.lastBaseUpdate,o!==i&&(o===null?d.firstBaseUpdate=u:o.next=u,d.lastBaseUpdate=s))}if(r!==null){var m=l.baseState;i=0,d=u=s=null,o=r;do{var h=o.lane&-536870913,f=h!==o.lane;if(f?(F&h)===h:(a&h)===h){h!==0&&h===nr&&(Cu=!0),d!==null&&(d=d.next={lane:0,tag:o.tag,payload:o.payload,callback:null,next:null});e:{var w=e,y=o;h=t;var x=n;switch(y.tag){case 1:if(w=y.payload,typeof w=="function"){m=w.call(x,m,h);break e}m=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=y.payload,h=typeof w=="function"?w.call(x,m,h):w,h==null)break e;m=me({},m,h);break e;case 2:Wn=!0}}h=o.callback,h!==null&&(e.flags|=64,f&&(e.flags|=8192),f=l.callbacks,f===null?l.callbacks=[h]:f.push(h))}else f={lane:h,tag:o.tag,payload:o.payload,callback:o.callback,next:null},d===null?(u=d=f,s=m):d=d.next=f,i|=h;if(o=o.next,o===null){if(o=l.shared.pending,o===null)break;f=o,o=f.next,f.next=null,l.lastBaseUpdate=f,l.shared.pending=null}}while(!0);d===null&&(s=m),l.baseState=s,l.firstBaseUpdate=u,l.lastBaseUpdate=d,r===null&&(l.shared.lanes=0),Ea|=i,e.lanes=i,e.memoizedState=m}}function Dv(e,t){if(typeof e!="function")throw Error(j(191,e));e.call(t)}function _v(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)Dv(n[e],t)}var ar=sn(null),Po=sn(0);function Oh(e,t){e=Dn,we(Po,e),we(ar,t),Dn=e|t.baseLanes}function ju(){we(Po,Dn),we(ar,ar.current)}function Gf(){Dn=Po.current,Ve(ar),Ve(Po)}var Sa=0,Q=null,se=null,ze=null,Zo=!1,Bl=!1,$a=!1,Ko=0,si=0,Hl=null,G1=0;function Oe(){throw Error(j(321))}function Yf(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!xt(e[n],t[n]))return!1;return!0}function Vf(e,t,n,a,l,r){return Sa=r,Q=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,H.H=e===null||e.memoizedState===null?ug:fg,$a=!1,r=n(a,l),$a=!1,Bl&&(r=Uv(t,n,a,l)),zv(e),r}function zv(e){H.H=Fo;var t=se!==null&&se.next!==null;if(Sa=0,ze=se=Q=null,Zo=!1,si=0,Hl=null,t)throw Error(j(300));e===null||Ye||(e=e.dependencies,e!==null&&Xo(e)&&(Ye=!0))}function Uv(e,t,n,a){Q=e;var l=0;do{if(Bl&&(Hl=null),si=0,Bl=!1,25<=l)throw Error(j(301));if(l+=1,ze=se=null,e.updateQueue!=null){var r=e.updateQueue;r.lastEffect=null,r.events=null,r.stores=null,r.memoCache!=null&&(r.memoCache.index=0)}H.H=K1,r=t(n,a)}while(Bl);return r}function Y1(){var e=H.H,t=e.useState()[0];return t=typeof t.then=="function"?Li(t):t,e=e.useState()[0],(se!==null?se.memoizedState:null)!==e&&(Q.flags|=1024),t}function Qf(){var e=Ko!==0;return Ko=0,e}function Xf(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Pf(e){if(Zo){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Zo=!1}Sa=0,ze=se=Q=null,Bl=!1,si=Ko=0,Hl=null}function nt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ze===null?Q.memoizedState=ze=e:ze=ze.next=e,ze}function Ue(){if(se===null){var e=Q.alternate;e=e!==null?e.memoizedState:null}else e=se.next;var t=ze===null?Q.memoizedState:ze.next;if(t!==null)ze=t,se=e;else{if(e===null)throw Q.alternate===null?Error(j(467)):Error(j(310));se=e,e={memoizedState:se.memoizedState,baseState:se.baseState,baseQueue:se.baseQueue,queue:se.queue,next:null},ze===null?Q.memoizedState=ze=e:ze=ze.next=e}return ze}function Zf(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Li(e){var t=si;return si+=1,Hl===null&&(Hl=[]),e=Mv(Hl,e,t),t=Q,(ze===null?t.memoizedState:ze.next)===null&&(t=t.alternate,H.H=t===null||t.memoizedState===null?ug:fg),e}function zs(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Li(e);if(e.$$typeof===wn)return Je(e)}throw Error(j(438,String(e)))}function Kf(e){var t=null,n=Q.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var a=Q.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(l){return l.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=Zf(),Q.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),a=0;a<e;a++)n[a]=Nx;return t.index++,n}function On(e,t){return typeof t=="function"?t(e):t}function Ao(e){var t=Ue();return Ff(t,se,e)}function Ff(e,t,n){var a=e.queue;if(a===null)throw Error(j(311));a.lastRenderedReducer=n;var l=e.baseQueue,r=a.pending;if(r!==null){if(l!==null){var i=l.next;l.next=r.next,r.next=i}t.baseQueue=l=r,a.pending=null}if(r=e.baseState,l===null)e.memoizedState=r;else{t=l.next;var o=i=null,s=null,u=t,d=!1;do{var m=u.lane&-536870913;if(m!==u.lane?(F&m)===m:(Sa&m)===m){var h=u.revertLane;if(h===0)s!==null&&(s=s.next={lane:0,revertLane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),m===nr&&(d=!0);else if((Sa&h)===h){u=u.next,h===nr&&(d=!0);continue}else m={lane:0,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},s===null?(o=s=m,i=r):s=s.next=m,Q.lanes|=h,Ea|=h;m=u.action,$a&&n(r,m),r=u.hasEagerState?u.eagerState:n(r,m)}else h={lane:m,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},s===null?(o=s=h,i=r):s=s.next=h,Q.lanes|=m,Ea|=m;u=u.next}while(u!==null&&u!==t);if(s===null?i=r:s.next=o,!xt(r,e.memoizedState)&&(Ye=!0,d&&(n=Ll,n!==null)))throw n;e.memoizedState=r,e.baseState=i,e.baseQueue=s,a.lastRenderedState=r}return l===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function Tc(e){var t=Ue(),n=t.queue;if(n===null)throw Error(j(311));n.lastRenderedReducer=e;var a=n.dispatch,l=n.pending,r=t.memoizedState;if(l!==null){n.pending=null;var i=l=l.next;do r=e(r,i.action),i=i.next;while(i!==l);xt(r,t.memoizedState)||(Ye=!0),t.memoizedState=r,t.baseQueue===null&&(t.baseState=r),n.lastRenderedState=r}return[r,a]}function Lv(e,t,n){var a=Q,l=Ue(),r=ee;if(r){if(n===void 0)throw Error(j(407));n=n()}else n=t();var i=!xt((se||l).memoizedState,n);i&&(l.memoizedState=n,Ye=!0),l=l.queue;var o=kv.bind(null,a,l,e);if(Bi(2048,8,o,[e]),l.getSnapshot!==t||i||ze!==null&&ze.memoizedState.tag&1){if(a.flags|=2048,lr(9,Us(),Hv.bind(null,a,l,n,t),null),fe===null)throw Error(j(349));r||Sa&124||Bv(a,t,n)}return n}function Bv(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Q.updateQueue,t===null?(t=Zf(),Q.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Hv(e,t,n,a){t.value=n,t.getSnapshot=a,qv(t)&&Gv(e)}function kv(e,t,n){return n(function(){qv(t)&&Gv(e)})}function qv(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!xt(e,n)}catch{return!0}}function Gv(e){var t=vr(e,2);t!==null&&yt(t,e,2)}function Ru(e){var t=nt();if(typeof e=="function"){var n=e;if(e=n(),$a){sa(!0);try{n()}finally{sa(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:On,lastRenderedState:e},t}function Yv(e,t,n,a){return e.baseState=n,Ff(e,se,typeof a=="function"?a:On)}function V1(e,t,n,a,l){if(Ls(e))throw Error(j(485));if(e=t.action,e!==null){var r={payload:l,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(i){r.listeners.push(i)}};H.T!==null?n(!0):r.isTransition=!1,a(r),n=t.pending,n===null?(r.next=t.pending=r,Vv(t,r)):(r.next=n.next,t.pending=n.next=r)}}function Vv(e,t){var n=t.action,a=t.payload,l=e.state;if(t.isTransition){var r=H.T,i={};H.T=i;try{var o=n(l,a),s=H.S;s!==null&&s(i,o),Mh(e,t,o)}catch(u){Ou(e,t,u)}finally{H.T=r}}else try{r=n(l,a),Mh(e,t,r)}catch(u){Ou(e,t,u)}}function Mh(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(a){Dh(e,t,a)},function(a){return Ou(e,t,a)}):Dh(e,t,n)}function Dh(e,t,n){t.status="fulfilled",t.value=n,Qv(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,Vv(e,n)))}function Ou(e,t,n){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=n,Qv(t),t=t.next;while(t!==a)}e.action=null}function Qv(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Xv(e,t){return t}function _h(e,t){if(ee){var n=fe.formState;if(n!==null){e:{var a=Q;if(ee){if(Ce){t:{for(var l=Ce,r=$t;l.nodeType!==8;){if(!r){l=null;break t}if(l=Yt(l.nextSibling),l===null){l=null;break t}}r=l.data,l=r==="F!"||r==="F"?l:null}if(l){Ce=Yt(l.nextSibling),a=l.data==="F!";break e}}Fa(a)}a=!1}a&&(t=n[0])}}return n=nt(),n.memoizedState=n.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Xv,lastRenderedState:t},n.queue=a,n=og.bind(null,Q,a),a.dispatch=n,a=Ru(!1),r=If.bind(null,Q,!1,a.queue),a=nt(),l={state:t,dispatch:null,action:e,pending:null},a.queue=l,n=V1.bind(null,Q,l,r,n),l.dispatch=n,a.memoizedState=e,[t,n,!1]}function zh(e){var t=Ue();return Pv(t,se,e)}function Pv(e,t,n){if(t=Ff(e,t,Xv)[0],e=Ao(On)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=Li(t)}catch(i){throw i===Ui?_s:i}else a=t;t=Ue();var l=t.queue,r=l.dispatch;return n!==t.memoizedState&&(Q.flags|=2048,lr(9,Us(),Q1.bind(null,l,n),null)),[a,r,e]}function Q1(e,t){e.action=t}function Uh(e){var t=Ue(),n=se;if(n!==null)return Pv(t,n,e);Ue(),t=t.memoizedState,n=Ue();var a=n.queue.dispatch;return n.memoizedState=e,[t,a,!1]}function lr(e,t,n,a){return e={tag:e,create:n,deps:a,inst:t,next:null},t=Q.updateQueue,t===null&&(t=Zf(),Q.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(a=n.next,n.next=e,e.next=a,t.lastEffect=e),e}function Us(){return{destroy:void 0,resource:void 0}}function Zv(){return Ue().memoizedState}function To(e,t,n,a){var l=nt();a=a===void 0?null:a,Q.flags|=e,l.memoizedState=lr(1|t,Us(),n,a)}function Bi(e,t,n,a){var l=Ue();a=a===void 0?null:a;var r=l.memoizedState.inst;se!==null&&a!==null&&Yf(a,se.memoizedState.deps)?l.memoizedState=lr(t,r,n,a):(Q.flags|=e,l.memoizedState=lr(1|t,r,n,a))}function Lh(e,t){To(8390656,8,e,t)}function Kv(e,t){Bi(2048,8,e,t)}function Fv(e,t){return Bi(4,2,e,t)}function Jv(e,t){return Bi(4,4,e,t)}function $v(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Wv(e,t,n){n=n!=null?n.concat([e]):null,Bi(4,4,$v.bind(null,t,e),n)}function Jf(){}function Iv(e,t){var n=Ue();t=t===void 0?null:t;var a=n.memoizedState;return t!==null&&Yf(t,a[1])?a[0]:(n.memoizedState=[e,t],e)}function eg(e,t){var n=Ue();t=t===void 0?null:t;var a=n.memoizedState;if(t!==null&&Yf(t,a[1]))return a[0];if(a=e(),$a){sa(!0);try{e()}finally{sa(!1)}}return n.memoizedState=[a,t],a}function $f(e,t,n){return n===void 0||Sa&1073741824?e.memoizedState=t:(e.memoizedState=n,e=Qg(),Q.lanes|=e,Ea|=e,n)}function tg(e,t,n,a){return xt(n,t)?n:ar.current!==null?(e=$f(e,n,a),xt(e,t)||(Ye=!0),e):Sa&42?(e=Qg(),Q.lanes|=e,Ea|=e,t):(Ye=!0,e.memoizedState=n)}function ng(e,t,n,a,l){var r=te.p;te.p=r!==0&&8>r?r:8;var i=H.T,o={};H.T=o,If(e,!1,t,n);try{var s=l(),u=H.S;if(u!==null&&u(o,s),s!==null&&typeof s=="object"&&typeof s.then=="function"){var d=q1(s,a);Jr(e,t,d,gt(e))}else Jr(e,t,a,gt(e))}catch(m){Jr(e,t,{then:function(){},status:"rejected",reason:m},gt())}finally{te.p=r,H.T=i}}function X1(){}function Mu(e,t,n,a){if(e.tag!==5)throw Error(j(476));var l=ag(e).queue;ng(e,l,t,Ya,n===null?X1:function(){return lg(e),n(a)})}function ag(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:Ya,baseState:Ya,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:On,lastRenderedState:Ya},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:On,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function lg(e){var t=ag(e).next.queue;Jr(e,t,{},gt())}function Wf(){return Je(hi)}function rg(){return Ue().memoizedState}function ig(){return Ue().memoizedState}function P1(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=gt();e=ma(n);var a=pa(t,e,n);a!==null&&(yt(a,t,n),Zr(a,t,n)),t={cache:Hf()},e.payload=t;return}t=t.return}}function Z1(e,t,n){var a=gt();n={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Ls(e)?sg(t,n):(n=zf(e,t,n,a),n!==null&&(yt(n,e,a),cg(n,t,a)))}function og(e,t,n){var a=gt();Jr(e,t,n,a)}function Jr(e,t,n,a){var l={lane:a,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ls(e))sg(t,l);else{var r=e.alternate;if(e.lanes===0&&(r===null||r.lanes===0)&&(r=t.lastRenderedReducer,r!==null))try{var i=t.lastRenderedState,o=r(i,n);if(l.hasEagerState=!0,l.eagerState=o,xt(o,i))return Ds(e,t,l,0),fe===null&&Ms(),!1}catch{}finally{}if(n=zf(e,t,l,a),n!==null)return yt(n,e,a),cg(n,t,a),!0}return!1}function If(e,t,n,a){if(a={lane:2,revertLane:od(),action:a,hasEagerState:!1,eagerState:null,next:null},Ls(e)){if(t)throw Error(j(479))}else t=zf(e,n,a,2),t!==null&&yt(t,e,2)}function Ls(e){var t=e.alternate;return e===Q||t!==null&&t===Q}function sg(e,t){Bl=Zo=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function cg(e,t,n){if(n&4194048){var a=t.lanes;a&=e.pendingLanes,n|=a,t.lanes=n,Ip(e,n)}}var Fo={readContext:Je,use:zs,useCallback:Oe,useContext:Oe,useEffect:Oe,useImperativeHandle:Oe,useLayoutEffect:Oe,useInsertionEffect:Oe,useMemo:Oe,useReducer:Oe,useRef:Oe,useState:Oe,useDebugValue:Oe,useDeferredValue:Oe,useTransition:Oe,useSyncExternalStore:Oe,useId:Oe,useHostTransitionStatus:Oe,useFormState:Oe,useActionState:Oe,useOptimistic:Oe,useMemoCache:Oe,useCacheRefresh:Oe},ug={readContext:Je,use:zs,useCallback:function(e,t){return nt().memoizedState=[e,t===void 0?null:t],e},useContext:Je,useEffect:Lh,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,To(4194308,4,$v.bind(null,t,e),n)},useLayoutEffect:function(e,t){return To(4194308,4,e,t)},useInsertionEffect:function(e,t){To(4,2,e,t)},useMemo:function(e,t){var n=nt();t=t===void 0?null:t;var a=e();if($a){sa(!0);try{e()}finally{sa(!1)}}return n.memoizedState=[a,t],a},useReducer:function(e,t,n){var a=nt();if(n!==void 0){var l=n(t);if($a){sa(!0);try{n(t)}finally{sa(!1)}}}else l=t;return a.memoizedState=a.baseState=l,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:l},a.queue=e,e=e.dispatch=Z1.bind(null,Q,e),[a.memoizedState,e]},useRef:function(e){var t=nt();return e={current:e},t.memoizedState=e},useState:function(e){e=Ru(e);var t=e.queue,n=og.bind(null,Q,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Jf,useDeferredValue:function(e,t){var n=nt();return $f(n,e,t)},useTransition:function(){var e=Ru(!1);return e=ng.bind(null,Q,e.queue,!0,!1),nt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var a=Q,l=nt();if(ee){if(n===void 0)throw Error(j(407));n=n()}else{if(n=t(),fe===null)throw Error(j(349));F&124||Bv(a,t,n)}l.memoizedState=n;var r={value:n,getSnapshot:t};return l.queue=r,Lh(kv.bind(null,a,r,e),[e]),a.flags|=2048,lr(9,Us(),Hv.bind(null,a,r,n,t),null),n},useId:function(){var e=nt(),t=fe.identifierPrefix;if(ee){var n=An,a=En;n=(a&~(1<<32-vt(a)-1)).toString(32)+n,t="«"+t+"R"+n,n=Ko++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=G1++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Wf,useFormState:_h,useActionState:_h,useOptimistic:function(e){var t=nt();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=If.bind(null,Q,!0,n),n.dispatch=t,[e,t]},useMemoCache:Kf,useCacheRefresh:function(){return nt().memoizedState=P1.bind(null,Q)}},fg={readContext:Je,use:zs,useCallback:Iv,useContext:Je,useEffect:Kv,useImperativeHandle:Wv,useInsertionEffect:Fv,useLayoutEffect:Jv,useMemo:eg,useReducer:Ao,useRef:Zv,useState:function(){return Ao(On)},useDebugValue:Jf,useDeferredValue:function(e,t){var n=Ue();return tg(n,se.memoizedState,e,t)},useTransition:function(){var e=Ao(On)[0],t=Ue().memoizedState;return[typeof e=="boolean"?e:Li(e),t]},useSyncExternalStore:Lv,useId:rg,useHostTransitionStatus:Wf,useFormState:zh,useActionState:zh,useOptimistic:function(e,t){var n=Ue();return Yv(n,se,e,t)},useMemoCache:Kf,useCacheRefresh:ig},K1={readContext:Je,use:zs,useCallback:Iv,useContext:Je,useEffect:Kv,useImperativeHandle:Wv,useInsertionEffect:Fv,useLayoutEffect:Jv,useMemo:eg,useReducer:Tc,useRef:Zv,useState:function(){return Tc(On)},useDebugValue:Jf,useDeferredValue:function(e,t){var n=Ue();return se===null?$f(n,e,t):tg(n,se.memoizedState,e,t)},useTransition:function(){var e=Tc(On)[0],t=Ue().memoizedState;return[typeof e=="boolean"?e:Li(e),t]},useSyncExternalStore:Lv,useId:rg,useHostTransitionStatus:Wf,useFormState:Uh,useActionState:Uh,useOptimistic:function(e,t){var n=Ue();return se!==null?Yv(n,se,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Kf,useCacheRefresh:ig},kl=null,ci=0;function no(e){var t=ci;return ci+=1,kl===null&&(kl=[]),Mv(kl,e,t)}function Rr(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function ao(e,t){throw t.$$typeof===Ax?Error(j(525)):(e=Object.prototype.toString.call(t),Error(j(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Bh(e){var t=e._init;return t(e._payload)}function dg(e){function t(v,g){if(e){var b=v.deletions;b===null?(v.deletions=[g],v.flags|=16):b.push(g)}}function n(v,g){if(!e)return null;for(;g!==null;)t(v,g),g=g.sibling;return null}function a(v){for(var g=new Map;v!==null;)v.key!==null?g.set(v.key,v):g.set(v.index,v),v=v.sibling;return g}function l(v,g){return v=Cn(v,g),v.index=0,v.sibling=null,v}function r(v,g,b){return v.index=b,e?(b=v.alternate,b!==null?(b=b.index,b<g?(v.flags|=67108866,g):b):(v.flags|=67108866,g)):(v.flags|=1048576,g)}function i(v){return e&&v.alternate===null&&(v.flags|=67108866),v}function o(v,g,b,S){return g===null||g.tag!==6?(g=wc(b,v.mode,S),g.return=v,g):(g=l(g,b),g.return=v,g)}function s(v,g,b,S){var E=b.type;return E===Sl?d(v,g,b.props.children,S,b.key):g!==null&&(g.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===$n&&Bh(E)===g.type)?(g=l(g,b.props),Rr(g,b),g.return=v,g):(g=wo(b.type,b.key,b.props,null,v.mode,S),Rr(g,b),g.return=v,g)}function u(v,g,b,S){return g===null||g.tag!==4||g.stateNode.containerInfo!==b.containerInfo||g.stateNode.implementation!==b.implementation?(g=Ec(b,v.mode,S),g.return=v,g):(g=l(g,b.children||[]),g.return=v,g)}function d(v,g,b,S,E){return g===null||g.tag!==7?(g=Va(b,v.mode,S,E),g.return=v,g):(g=l(g,b),g.return=v,g)}function m(v,g,b){if(typeof g=="string"&&g!==""||typeof g=="number"||typeof g=="bigint")return g=wc(""+g,v.mode,b),g.return=v,g;if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Fi:return b=wo(g.type,g.key,g.props,null,v.mode,b),Rr(b,g),b.return=v,b;case Ur:return g=Ec(g,v.mode,b),g.return=v,g;case $n:var S=g._init;return g=S(g._payload),m(v,g,b)}if(Lr(g)||Tr(g))return g=Va(g,v.mode,b,null),g.return=v,g;if(typeof g.then=="function")return m(v,no(g),b);if(g.$$typeof===wn)return m(v,eo(v,g),b);ao(v,g)}return null}function h(v,g,b,S){var E=g!==null?g.key:null;if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return E!==null?null:o(v,g,""+b,S);if(typeof b=="object"&&b!==null){switch(b.$$typeof){case Fi:return b.key===E?s(v,g,b,S):null;case Ur:return b.key===E?u(v,g,b,S):null;case $n:return E=b._init,b=E(b._payload),h(v,g,b,S)}if(Lr(b)||Tr(b))return E!==null?null:d(v,g,b,S,null);if(typeof b.then=="function")return h(v,g,no(b),S);if(b.$$typeof===wn)return h(v,g,eo(v,b),S);ao(v,b)}return null}function f(v,g,b,S,E){if(typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint")return v=v.get(b)||null,o(g,v,""+S,E);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case Fi:return v=v.get(S.key===null?b:S.key)||null,s(g,v,S,E);case Ur:return v=v.get(S.key===null?b:S.key)||null,u(g,v,S,E);case $n:var T=S._init;return S=T(S._payload),f(v,g,b,S,E)}if(Lr(S)||Tr(S))return v=v.get(b)||null,d(g,v,S,E,null);if(typeof S.then=="function")return f(v,g,b,no(S),E);if(S.$$typeof===wn)return f(v,g,b,eo(g,S),E);ao(g,S)}return null}function w(v,g,b,S){for(var E=null,T=null,A=g,C=g=0,O=null;A!==null&&C<b.length;C++){A.index>C?(O=A,A=null):O=A.sibling;var M=h(v,A,b[C],S);if(M===null){A===null&&(A=O);break}e&&A&&M.alternate===null&&t(v,A),g=r(M,g,C),T===null?E=M:T.sibling=M,T=M,A=O}if(C===b.length)return n(v,A),ee&&_a(v,C),E;if(A===null){for(;C<b.length;C++)A=m(v,b[C],S),A!==null&&(g=r(A,g,C),T===null?E=A:T.sibling=A,T=A);return ee&&_a(v,C),E}for(A=a(A);C<b.length;C++)O=f(A,v,C,b[C],S),O!==null&&(e&&O.alternate!==null&&A.delete(O.key===null?C:O.key),g=r(O,g,C),T===null?E=O:T.sibling=O,T=O);return e&&A.forEach(function(B){return t(v,B)}),ee&&_a(v,C),E}function y(v,g,b,S){if(b==null)throw Error(j(151));for(var E=null,T=null,A=g,C=g=0,O=null,M=b.next();A!==null&&!M.done;C++,M=b.next()){A.index>C?(O=A,A=null):O=A.sibling;var B=h(v,A,M.value,S);if(B===null){A===null&&(A=O);break}e&&A&&B.alternate===null&&t(v,A),g=r(B,g,C),T===null?E=B:T.sibling=B,T=B,A=O}if(M.done)return n(v,A),ee&&_a(v,C),E;if(A===null){for(;!M.done;C++,M=b.next())M=m(v,M.value,S),M!==null&&(g=r(M,g,C),T===null?E=M:T.sibling=M,T=M);return ee&&_a(v,C),E}for(A=a(A);!M.done;C++,M=b.next())M=f(A,v,C,M.value,S),M!==null&&(e&&M.alternate!==null&&A.delete(M.key===null?C:M.key),g=r(M,g,C),T===null?E=M:T.sibling=M,T=M);return e&&A.forEach(function(L){return t(v,L)}),ee&&_a(v,C),E}function x(v,g,b,S){if(typeof b=="object"&&b!==null&&b.type===Sl&&b.key===null&&(b=b.props.children),typeof b=="object"&&b!==null){switch(b.$$typeof){case Fi:e:{for(var E=b.key;g!==null;){if(g.key===E){if(E=b.type,E===Sl){if(g.tag===7){n(v,g.sibling),S=l(g,b.props.children),S.return=v,v=S;break e}}else if(g.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===$n&&Bh(E)===g.type){n(v,g.sibling),S=l(g,b.props),Rr(S,b),S.return=v,v=S;break e}n(v,g);break}else t(v,g);g=g.sibling}b.type===Sl?(S=Va(b.props.children,v.mode,S,b.key),S.return=v,v=S):(S=wo(b.type,b.key,b.props,null,v.mode,S),Rr(S,b),S.return=v,v=S)}return i(v);case Ur:e:{for(E=b.key;g!==null;){if(g.key===E)if(g.tag===4&&g.stateNode.containerInfo===b.containerInfo&&g.stateNode.implementation===b.implementation){n(v,g.sibling),S=l(g,b.children||[]),S.return=v,v=S;break e}else{n(v,g);break}else t(v,g);g=g.sibling}S=Ec(b,v.mode,S),S.return=v,v=S}return i(v);case $n:return E=b._init,b=E(b._payload),x(v,g,b,S)}if(Lr(b))return w(v,g,b,S);if(Tr(b)){if(E=Tr(b),typeof E!="function")throw Error(j(150));return b=E.call(b),y(v,g,b,S)}if(typeof b.then=="function")return x(v,g,no(b),S);if(b.$$typeof===wn)return x(v,g,eo(v,b),S);ao(v,b)}return typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint"?(b=""+b,g!==null&&g.tag===6?(n(v,g.sibling),S=l(g,b),S.return=v,v=S):(n(v,g),S=wc(b,v.mode,S),S.return=v,v=S),i(v)):n(v,g)}return function(v,g,b,S){try{ci=0;var E=x(v,g,b,S);return kl=null,E}catch(A){if(A===Ui||A===_s)throw A;var T=mt(29,A,null,v.mode);return T.lanes=S,T.return=v,T}finally{}}}var rr=dg(!0),hg=dg(!1),Mt=sn(null),ln=null;function ea(e){var t=e.alternate;we(He,He.current&1),we(Mt,e),ln===null&&(t===null||ar.current!==null||t.memoizedState!==null)&&(ln=e)}function mg(e){if(e.tag===22){if(we(He,He.current),we(Mt,e),ln===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(ln=e)}}else ta()}function ta(){we(He,He.current),we(Mt,Mt.current)}function Nn(e){Ve(Mt),ln===e&&(ln=null),Ve(He)}var He=sn(0);function Jo(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||Ku(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Nc(e,t,n,a){t=e.memoizedState,n=n(a,t),n=n==null?t:me({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Du={enqueueSetState:function(e,t,n){e=e._reactInternals;var a=gt(),l=ma(a);l.payload=t,n!=null&&(l.callback=n),t=pa(e,l,a),t!==null&&(yt(t,e,a),Zr(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var a=gt(),l=ma(a);l.tag=1,l.payload=t,n!=null&&(l.callback=n),t=pa(e,l,a),t!==null&&(yt(t,e,a),Zr(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=gt(),a=ma(n);a.tag=2,t!=null&&(a.callback=t),t=pa(e,a,n),t!==null&&(yt(t,e,n),Zr(t,e,n))}};function Hh(e,t,n,a,l,r,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,r,i):t.prototype&&t.prototype.isPureReactComponent?!ii(n,a)||!ii(l,r):!0}function kh(e,t,n,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,a),t.state!==e&&Du.enqueueReplaceState(t,t.state,null)}function Wa(e,t){var n=t;if("ref"in t){n={};for(var a in t)a!=="ref"&&(n[a]=t[a])}if(e=e.defaultProps){n===t&&(n=me({},n));for(var l in e)n[l]===void 0&&(n[l]=e[l])}return n}var $o=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function pg(e){$o(e)}function vg(e){console.error(e)}function gg(e){$o(e)}function Wo(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function qh(e,t,n){try{var a=e.onCaughtError;a(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(l){setTimeout(function(){throw l})}}function _u(e,t,n){return n=ma(n),n.tag=3,n.payload={element:null},n.callback=function(){Wo(e,t)},n}function yg(e){return e=ma(e),e.tag=3,e}function bg(e,t,n,a){var l=n.type.getDerivedStateFromError;if(typeof l=="function"){var r=a.value;e.payload=function(){return l(r)},e.callback=function(){qh(t,n,a)}}var i=n.stateNode;i!==null&&typeof i.componentDidCatch=="function"&&(e.callback=function(){qh(t,n,a),typeof l!="function"&&(va===null?va=new Set([this]):va.add(this));var o=a.stack;this.componentDidCatch(a.value,{componentStack:o!==null?o:""})})}function F1(e,t,n,a,l){if(n.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=n.alternate,t!==null&&_i(t,n,l,!0),n=Mt.current,n!==null){switch(n.tag){case 13:return ln===null?Gu():n.alternate===null&&je===0&&(je=3),n.flags&=-257,n.flags|=65536,n.lanes=l,a===Tu?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([a]):t.add(a),Bc(e,a,l)),!1;case 22:return n.flags|=65536,a===Tu?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([a]):n.add(a)),Bc(e,a,l)),!1}throw Error(j(435,n.tag))}return Bc(e,a,l),Gu(),!1}if(ee)return t=Mt.current,t!==null?(!(t.flags&65536)&&(t.flags|=256),t.flags|=65536,t.lanes=l,a!==xu&&(e=Error(j(422),{cause:a}),oi(Rt(e,n)))):(a!==xu&&(t=Error(j(423),{cause:a}),oi(Rt(t,n))),e=e.current.alternate,e.flags|=65536,l&=-l,e.lanes|=l,a=Rt(a,n),l=_u(e.stateNode,a,l),Ac(e,l),je!==4&&(je=2)),!1;var r=Error(j(520),{cause:a});if(r=Rt(r,n),Ir===null?Ir=[r]:Ir.push(r),je!==4&&(je=2),t===null)return!0;a=Rt(a,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=l&-l,n.lanes|=e,e=_u(n.stateNode,a,e),Ac(n,e),!1;case 1:if(t=n.type,r=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||r!==null&&typeof r.componentDidCatch=="function"&&(va===null||!va.has(r))))return n.flags|=65536,l&=-l,n.lanes|=l,l=yg(l),bg(l,e,n,a),Ac(n,l),!1}n=n.return}while(n!==null);return!1}var xg=Error(j(461)),Ye=!1;function Qe(e,t,n,a){t.child=e===null?hg(t,null,n,a):rr(t,e.child,n,a)}function Gh(e,t,n,a,l){n=n.render;var r=t.ref;if("ref"in a){var i={};for(var o in a)o!=="ref"&&(i[o]=a[o])}else i=a;return Ja(t),a=Vf(e,t,n,i,r,l),o=Qf(),e!==null&&!Ye?(Xf(e,t,l),Mn(e,t,l)):(ee&&o&&Lf(t),t.flags|=1,Qe(e,t,a,l),t.child)}function Yh(e,t,n,a,l){if(e===null){var r=n.type;return typeof r=="function"&&!Uf(r)&&r.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=r,Sg(e,t,r,a,l)):(e=wo(n.type,null,a,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(r=e.child,!ed(e,l)){var i=r.memoizedProps;if(n=n.compare,n=n!==null?n:ii,n(i,a)&&e.ref===t.ref)return Mn(e,t,l)}return t.flags|=1,e=Cn(r,a),e.ref=t.ref,e.return=t,t.child=e}function Sg(e,t,n,a,l){if(e!==null){var r=e.memoizedProps;if(ii(r,a)&&e.ref===t.ref)if(Ye=!1,t.pendingProps=a=r,ed(e,l))e.flags&131072&&(Ye=!0);else return t.lanes=e.lanes,Mn(e,t,l)}return zu(e,t,n,a,l)}function wg(e,t,n){var a=t.pendingProps,l=a.children,r=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if(t.flags&128){if(a=r!==null?r.baseLanes|n:n,e!==null){for(l=t.child=e.child,r=0;l!==null;)r=r|l.lanes|l.childLanes,l=l.sibling;t.childLanes=r&~a}else t.childLanes=0,t.child=null;return Vh(e,t,a,n)}if(n&536870912)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Eo(t,r!==null?r.cachePool:null),r!==null?Oh(t,r):ju(),mg(t);else return t.lanes=t.childLanes=536870912,Vh(e,t,r!==null?r.baseLanes|n:n,n)}else r!==null?(Eo(t,r.cachePool),Oh(t,r),ta(),t.memoizedState=null):(e!==null&&Eo(t,null),ju(),ta());return Qe(e,t,l,n),t.child}function Vh(e,t,n,a){var l=kf();return l=l===null?null:{parent:Be._currentValue,pool:l},t.memoizedState={baseLanes:n,cachePool:l},e!==null&&Eo(t,null),ju(),mg(t),e!==null&&_i(e,t,a,!0),null}function No(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(j(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function zu(e,t,n,a,l){return Ja(t),n=Vf(e,t,n,a,void 0,l),a=Qf(),e!==null&&!Ye?(Xf(e,t,l),Mn(e,t,l)):(ee&&a&&Lf(t),t.flags|=1,Qe(e,t,n,l),t.child)}function Qh(e,t,n,a,l,r){return Ja(t),t.updateQueue=null,n=Uv(t,a,n,l),zv(e),a=Qf(),e!==null&&!Ye?(Xf(e,t,r),Mn(e,t,r)):(ee&&a&&Lf(t),t.flags|=1,Qe(e,t,n,r),t.child)}function Xh(e,t,n,a,l){if(Ja(t),t.stateNode===null){var r=Rl,i=n.contextType;typeof i=="object"&&i!==null&&(r=Je(i)),r=new n(a,r),t.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=Du,t.stateNode=r,r._reactInternals=t,r=t.stateNode,r.props=a,r.state=t.memoizedState,r.refs={},qf(t),i=n.contextType,r.context=typeof i=="object"&&i!==null?Je(i):Rl,r.state=t.memoizedState,i=n.getDerivedStateFromProps,typeof i=="function"&&(Nc(t,n,i,a),r.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(i=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),i!==r.state&&Du.enqueueReplaceState(r,r.state,null),Fr(t,a,r,l),Kr(),r.state=t.memoizedState),typeof r.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){r=t.stateNode;var o=t.memoizedProps,s=Wa(n,o);r.props=s;var u=r.context,d=n.contextType;i=Rl,typeof d=="object"&&d!==null&&(i=Je(d));var m=n.getDerivedStateFromProps;d=typeof m=="function"||typeof r.getSnapshotBeforeUpdate=="function",o=t.pendingProps!==o,d||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(o||u!==i)&&kh(t,r,a,i),Wn=!1;var h=t.memoizedState;r.state=h,Fr(t,a,r,l),Kr(),u=t.memoizedState,o||h!==u||Wn?(typeof m=="function"&&(Nc(t,n,m,a),u=t.memoizedState),(s=Wn||Hh(t,n,s,a,h,u,i))?(d||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"&&(t.flags|=4194308)):(typeof r.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=u),r.props=a,r.state=u,r.context=i,a=s):(typeof r.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{r=t.stateNode,Nu(e,t),i=t.memoizedProps,d=Wa(n,i),r.props=d,m=t.pendingProps,h=r.context,u=n.contextType,s=Rl,typeof u=="object"&&u!==null&&(s=Je(u)),o=n.getDerivedStateFromProps,(u=typeof o=="function"||typeof r.getSnapshotBeforeUpdate=="function")||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(i!==m||h!==s)&&kh(t,r,a,s),Wn=!1,h=t.memoizedState,r.state=h,Fr(t,a,r,l),Kr();var f=t.memoizedState;i!==m||h!==f||Wn||e!==null&&e.dependencies!==null&&Xo(e.dependencies)?(typeof o=="function"&&(Nc(t,n,o,a),f=t.memoizedState),(d=Wn||Hh(t,n,d,a,h,f,s)||e!==null&&e.dependencies!==null&&Xo(e.dependencies))?(u||typeof r.UNSAFE_componentWillUpdate!="function"&&typeof r.componentWillUpdate!="function"||(typeof r.componentWillUpdate=="function"&&r.componentWillUpdate(a,f,s),typeof r.UNSAFE_componentWillUpdate=="function"&&r.UNSAFE_componentWillUpdate(a,f,s)),typeof r.componentDidUpdate=="function"&&(t.flags|=4),typeof r.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof r.componentDidUpdate!="function"||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=f),r.props=a,r.state=f,r.context=s,a=d):(typeof r.componentDidUpdate!="function"||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),a=!1)}return r=a,No(e,t),a=(t.flags&128)!==0,r||a?(r=t.stateNode,n=a&&typeof n.getDerivedStateFromError!="function"?null:r.render(),t.flags|=1,e!==null&&a?(t.child=rr(t,e.child,null,l),t.child=rr(t,null,n,l)):Qe(e,t,n,l),t.memoizedState=r.state,e=t.child):e=Mn(e,t,l),e}function Ph(e,t,n,a){return Di(),t.flags|=256,Qe(e,t,n,a),t.child}var Cc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function jc(e){return{baseLanes:e,cachePool:Rv()}}function Rc(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=Ot),e}function Eg(e,t,n){var a=t.pendingProps,l=!1,r=(t.flags&128)!==0,i;if((i=r)||(i=e!==null&&e.memoizedState===null?!1:(He.current&2)!==0),i&&(l=!0,t.flags&=-129),i=(t.flags&32)!==0,t.flags&=-33,e===null){if(ee){if(l?ea(t):ta(),ee){var o=Ce,s;if(s=o){e:{for(s=o,o=$t;s.nodeType!==8;){if(!o){o=null;break e}if(s=Yt(s.nextSibling),s===null){o=null;break e}}o=s}o!==null?(t.memoizedState={dehydrated:o,treeContext:Qa!==null?{id:En,overflow:An}:null,retryLane:536870912,hydrationErrors:null},s=mt(18,null,null,0),s.stateNode=o,s.return=t,t.child=s,Ie=t,Ce=null,s=!0):s=!1}s||Fa(t)}if(o=t.memoizedState,o!==null&&(o=o.dehydrated,o!==null))return Ku(o)?t.lanes=32:t.lanes=536870912,null;Nn(t)}return o=a.children,a=a.fallback,l?(ta(),l=t.mode,o=Io({mode:"hidden",children:o},l),a=Va(a,l,n,null),o.return=t,a.return=t,o.sibling=a,t.child=o,l=t.child,l.memoizedState=jc(n),l.childLanes=Rc(e,i,n),t.memoizedState=Cc,a):(ea(t),Uu(t,o))}if(s=e.memoizedState,s!==null&&(o=s.dehydrated,o!==null)){if(r)t.flags&256?(ea(t),t.flags&=-257,t=Oc(e,t,n)):t.memoizedState!==null?(ta(),t.child=e.child,t.flags|=128,t=null):(ta(),l=a.fallback,o=t.mode,a=Io({mode:"visible",children:a.children},o),l=Va(l,o,n,null),l.flags|=2,a.return=t,l.return=t,a.sibling=l,t.child=a,rr(t,e.child,null,n),a=t.child,a.memoizedState=jc(n),a.childLanes=Rc(e,i,n),t.memoizedState=Cc,t=l);else if(ea(t),Ku(o)){if(i=o.nextSibling&&o.nextSibling.dataset,i)var u=i.dgst;i=u,a=Error(j(419)),a.stack="",a.digest=i,oi({value:a,source:null,stack:null}),t=Oc(e,t,n)}else if(Ye||_i(e,t,n,!1),i=(n&e.childLanes)!==0,Ye||i){if(i=fe,i!==null&&(a=n&-n,a=a&42?1:Af(a),a=a&(i.suspendedLanes|n)?0:a,a!==0&&a!==s.retryLane))throw s.retryLane=a,vr(e,a),yt(i,e,a),xg;o.data==="$?"||Gu(),t=Oc(e,t,n)}else o.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=s.treeContext,Ce=Yt(o.nextSibling),Ie=t,ee=!0,Xa=null,$t=!1,e!==null&&(Nt[Ct++]=En,Nt[Ct++]=An,Nt[Ct++]=Qa,En=e.id,An=e.overflow,Qa=t),t=Uu(t,a.children),t.flags|=4096);return t}return l?(ta(),l=a.fallback,o=t.mode,s=e.child,u=s.sibling,a=Cn(s,{mode:"hidden",children:a.children}),a.subtreeFlags=s.subtreeFlags&65011712,u!==null?l=Cn(u,l):(l=Va(l,o,n,null),l.flags|=2),l.return=t,a.return=t,a.sibling=l,t.child=a,a=l,l=t.child,o=e.child.memoizedState,o===null?o=jc(n):(s=o.cachePool,s!==null?(u=Be._currentValue,s=s.parent!==u?{parent:u,pool:u}:s):s=Rv(),o={baseLanes:o.baseLanes|n,cachePool:s}),l.memoizedState=o,l.childLanes=Rc(e,i,n),t.memoizedState=Cc,a):(ea(t),n=e.child,e=n.sibling,n=Cn(n,{mode:"visible",children:a.children}),n.return=t,n.sibling=null,e!==null&&(i=t.deletions,i===null?(t.deletions=[e],t.flags|=16):i.push(e)),t.child=n,t.memoizedState=null,n)}function Uu(e,t){return t=Io({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Io(e,t){return e=mt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Oc(e,t,n){return rr(t,e.child,null,n),e=Uu(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Zh(e,t,n){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),wu(e.return,t,n)}function Mc(e,t,n,a,l){var r=e.memoizedState;r===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:n,tailMode:l}:(r.isBackwards=t,r.rendering=null,r.renderingStartTime=0,r.last=a,r.tail=n,r.tailMode=l)}function Ag(e,t,n){var a=t.pendingProps,l=a.revealOrder,r=a.tail;if(Qe(e,t,a.children,n),a=He.current,a&2)a=a&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Zh(e,n,t);else if(e.tag===19)Zh(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(we(He,a),l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&Jo(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),Mc(t,!1,l,n,r);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&Jo(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}Mc(t,!0,n,null,r);break;case"together":Mc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Mn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Ea|=t.lanes,!(n&t.childLanes))if(e!==null){if(_i(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(j(153));if(t.child!==null){for(e=t.child,n=Cn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Cn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function ed(e,t){return e.lanes&t?!0:(e=e.dependencies,!!(e!==null&&Xo(e)))}function J1(e,t,n){switch(t.tag){case 3:Bo(t,t.stateNode.containerInfo),In(t,Be,e.memoizedState.cache),Di();break;case 27:case 5:uu(t);break;case 4:Bo(t,t.stateNode.containerInfo);break;case 10:In(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(ea(t),t.flags|=128,null):n&t.child.childLanes?Eg(e,t,n):(ea(t),e=Mn(e,t,n),e!==null?e.sibling:null);ea(t);break;case 19:var l=(e.flags&128)!==0;if(a=(n&t.childLanes)!==0,a||(_i(e,t,n,!1),a=(n&t.childLanes)!==0),l){if(a)return Ag(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),we(He,He.current),a)break;return null;case 22:case 23:return t.lanes=0,wg(e,t,n);case 24:In(t,Be,e.memoizedState.cache)}return Mn(e,t,n)}function Tg(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)Ye=!0;else{if(!ed(e,n)&&!(t.flags&128))return Ye=!1,J1(e,t,n);Ye=!!(e.flags&131072)}else Ye=!1,ee&&t.flags&1048576&&Cv(t,Qo,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,l=a._init;if(a=l(a._payload),t.type=a,typeof a=="function")Uf(a)?(e=Wa(a,e),t.tag=1,t=Xh(null,t,a,e,n)):(t.tag=0,t=zu(null,t,a,e,n));else{if(a!=null){if(l=a.$$typeof,l===Sf){t.tag=11,t=Gh(null,t,a,e,n);break e}else if(l===wf){t.tag=14,t=Yh(null,t,a,e,n);break e}}throw t=su(a)||a,Error(j(306,t,""))}}return t;case 0:return zu(e,t,t.type,t.pendingProps,n);case 1:return a=t.type,l=Wa(a,t.pendingProps),Xh(e,t,a,l,n);case 3:e:{if(Bo(t,t.stateNode.containerInfo),e===null)throw Error(j(387));a=t.pendingProps;var r=t.memoizedState;l=r.element,Nu(e,t),Fr(t,a,null,n);var i=t.memoizedState;if(a=i.cache,In(t,Be,a),a!==r.cache&&Eu(t,[Be],n,!0),Kr(),a=i.element,r.isDehydrated)if(r={element:a,isDehydrated:!1,cache:i.cache},t.updateQueue.baseState=r,t.memoizedState=r,t.flags&256){t=Ph(e,t,a,n);break e}else if(a!==l){l=Rt(Error(j(424)),t),oi(l),t=Ph(e,t,a,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Ce=Yt(e.firstChild),Ie=t,ee=!0,Xa=null,$t=!0,n=hg(t,null,a,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Di(),a===l){t=Mn(e,t,n);break e}Qe(e,t,a,n)}t=t.child}return t;case 26:return No(e,t),e===null?(n=um(t.type,null,t.pendingProps,null))?t.memoizedState=n:ee||(n=t.type,e=t.pendingProps,a=is(ha.current).createElement(n),a[Fe]=t,a[ot]=e,Pe(a,n,e),Ge(a),t.stateNode=a):t.memoizedState=um(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return uu(t),e===null&&ee&&(a=t.stateNode=fy(t.type,t.pendingProps,ha.current),Ie=t,$t=!0,l=Ce,Ca(t.type)?(Fu=l,Ce=Yt(a.firstChild)):Ce=l),Qe(e,t,t.pendingProps.children,n),No(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&ee&&((l=a=Ce)&&(a=AS(a,t.type,t.pendingProps,$t),a!==null?(t.stateNode=a,Ie=t,Ce=Yt(a.firstChild),$t=!1,l=!0):l=!1),l||Fa(t)),uu(t),l=t.type,r=t.pendingProps,i=e!==null?e.memoizedProps:null,a=r.children,Pu(l,r)?a=null:i!==null&&Pu(l,i)&&(t.flags|=32),t.memoizedState!==null&&(l=Vf(e,t,Y1,null,null,n),hi._currentValue=l),No(e,t),Qe(e,t,a,n),t.child;case 6:return e===null&&ee&&((e=n=Ce)&&(n=TS(n,t.pendingProps,$t),n!==null?(t.stateNode=n,Ie=t,Ce=null,e=!0):e=!1),e||Fa(t)),null;case 13:return Eg(e,t,n);case 4:return Bo(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=rr(t,null,a,n):Qe(e,t,a,n),t.child;case 11:return Gh(e,t,t.type,t.pendingProps,n);case 7:return Qe(e,t,t.pendingProps,n),t.child;case 8:return Qe(e,t,t.pendingProps.children,n),t.child;case 12:return Qe(e,t,t.pendingProps.children,n),t.child;case 10:return a=t.pendingProps,In(t,t.type,a.value),Qe(e,t,a.children,n),t.child;case 9:return l=t.type._context,a=t.pendingProps.children,Ja(t),l=Je(l),a=a(l),t.flags|=1,Qe(e,t,a,n),t.child;case 14:return Yh(e,t,t.type,t.pendingProps,n);case 15:return Sg(e,t,t.type,t.pendingProps,n);case 19:return Ag(e,t,n);case 31:return a=t.pendingProps,n=t.mode,a={mode:a.mode,children:a.children},e===null?(n=Io(a,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=Cn(e.child,a),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return wg(e,t,n);case 24:return Ja(t),a=Je(Be),e===null?(l=kf(),l===null&&(l=fe,r=Hf(),l.pooledCache=r,r.refCount++,r!==null&&(l.pooledCacheLanes|=n),l=r),t.memoizedState={parent:a,cache:l},qf(t),In(t,Be,l)):(e.lanes&n&&(Nu(e,t),Fr(t,null,null,n),Kr()),l=e.memoizedState,r=t.memoizedState,l.parent!==a?(l={parent:a,cache:a},t.memoizedState=l,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=l),In(t,Be,a)):(a=r.cache,In(t,Be,a),a!==l.cache&&Eu(t,[Be],n,!0))),Qe(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(j(156,t.tag))}function mn(e){e.flags|=4}function Kh(e,t){if(t.type!=="stylesheet"||t.state.loading&4)e.flags&=-16777217;else if(e.flags|=16777216,!my(t)){if(t=Mt.current,t!==null&&((F&4194048)===F?ln!==null:(F&62914560)!==F&&!(F&536870912)||t!==ln))throw Pr=Tu,Ov;e.flags|=8192}}function lo(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?$p():536870912,e.lanes|=t,ir|=t)}function Or(e,t){if(!ee)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var a=null;n!==null;)n.alternate!==null&&(a=n),n=n.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function Ae(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,a=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,a|=l.subtreeFlags&65011712,a|=l.flags&65011712,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,a|=l.subtreeFlags,a|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=a,e.childLanes=n,t}function $1(e,t,n){var a=t.pendingProps;switch(Bf(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ae(t),null;case 1:return Ae(t),null;case 3:return n=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),jn(Be),Il(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(jr(t)?mn(t):e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ah())),Ae(t),null;case 26:return n=t.memoizedState,e===null?(mn(t),n!==null?(Ae(t),Kh(t,n)):(Ae(t),t.flags&=-16777217)):n?n!==e.memoizedState?(mn(t),Ae(t),Kh(t,n)):(Ae(t),t.flags&=-16777217):(e.memoizedProps!==a&&mn(t),Ae(t),t.flags&=-16777217),null;case 27:Ho(t),n=ha.current;var l=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&mn(t);else{if(!a){if(t.stateNode===null)throw Error(j(166));return Ae(t),null}e=nn.current,jr(t)?wh(t):(e=fy(l,a,n),t.stateNode=e,mn(t))}return Ae(t),null;case 5:if(Ho(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&mn(t);else{if(!a){if(t.stateNode===null)throw Error(j(166));return Ae(t),null}if(e=nn.current,jr(t))wh(t);else{switch(l=is(ha.current),e){case 1:e=l.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=l.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=l.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=l.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?l.createElement("select",{is:a.is}):l.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?l.createElement(n,{is:a.is}):l.createElement(n)}}e[Fe]=t,e[ot]=a;e:for(l=t.child;l!==null;){if(l.tag===5||l.tag===6)e.appendChild(l.stateNode);else if(l.tag!==4&&l.tag!==27&&l.child!==null){l.child.return=l,l=l.child;continue}if(l===t)break e;for(;l.sibling===null;){if(l.return===null||l.return===t)break e;l=l.return}l.sibling.return=l.return,l=l.sibling}t.stateNode=e;e:switch(Pe(e,n,a),n){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&mn(t)}}return Ae(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&mn(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(j(166));if(e=ha.current,jr(t)){if(e=t.stateNode,n=t.memoizedProps,a=null,l=Ie,l!==null)switch(l.tag){case 27:case 5:a=l.memoizedProps}e[Fe]=t,e=!!(e.nodeValue===n||a!==null&&a.suppressHydrationWarning===!0||sy(e.nodeValue,n)),e||Fa(t)}else e=is(e).createTextNode(a),e[Fe]=t,t.stateNode=e}return Ae(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(l=jr(t),a!==null&&a.dehydrated!==null){if(e===null){if(!l)throw Error(j(318));if(l=t.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error(j(317));l[Fe]=t}else Di(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Ae(t),l=!1}else l=Ah(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=l),l=!0;if(!l)return t.flags&256?(Nn(t),t):(Nn(t),null)}if(Nn(t),t.flags&128)return t.lanes=n,t;if(n=a!==null,e=e!==null&&e.memoizedState!==null,n){a=t.child,l=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(l=a.alternate.memoizedState.cachePool.pool);var r=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(r=a.memoizedState.cachePool.pool),r!==l&&(a.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),lo(t,t.updateQueue),Ae(t),null;case 4:return Il(),e===null&&sd(t.stateNode.containerInfo),Ae(t),null;case 10:return jn(t.type),Ae(t),null;case 19:if(Ve(He),l=t.memoizedState,l===null)return Ae(t),null;if(a=(t.flags&128)!==0,r=l.rendering,r===null)if(a)Or(l,!1);else{if(je!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(r=Jo(e),r!==null){for(t.flags|=128,Or(l,!1),e=r.updateQueue,t.updateQueue=e,lo(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)Nv(n,e),n=n.sibling;return we(He,He.current&1|2),t.child}e=e.sibling}l.tail!==null&&an()>ts&&(t.flags|=128,a=!0,Or(l,!1),t.lanes=4194304)}else{if(!a)if(e=Jo(r),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,lo(t,e),Or(l,!0),l.tail===null&&l.tailMode==="hidden"&&!r.alternate&&!ee)return Ae(t),null}else 2*an()-l.renderingStartTime>ts&&n!==536870912&&(t.flags|=128,a=!0,Or(l,!1),t.lanes=4194304);l.isBackwards?(r.sibling=t.child,t.child=r):(e=l.last,e!==null?e.sibling=r:t.child=r,l.last=r)}return l.tail!==null?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=an(),t.sibling=null,e=He.current,we(He,a?e&1|2:e&1),t):(Ae(t),null);case 22:case 23:return Nn(t),Gf(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?n&536870912&&!(t.flags&128)&&(Ae(t),t.subtreeFlags&6&&(t.flags|=8192)):Ae(t),n=t.updateQueue,n!==null&&lo(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==n&&(t.flags|=2048),e!==null&&Ve(Pa),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),jn(Be),Ae(t),null;case 25:return null;case 30:return null}throw Error(j(156,t.tag))}function W1(e,t){switch(Bf(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return jn(Be),Il(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Ho(t),null;case 13:if(Nn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(j(340));Di()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Ve(He),null;case 4:return Il(),null;case 10:return jn(t.type),null;case 22:case 23:return Nn(t),Gf(),e!==null&&Ve(Pa),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return jn(Be),null;case 25:return null;default:return null}}function Ng(e,t){switch(Bf(t),t.tag){case 3:jn(Be),Il();break;case 26:case 27:case 5:Ho(t);break;case 4:Il();break;case 13:Nn(t);break;case 19:Ve(He);break;case 10:jn(t.type);break;case 22:case 23:Nn(t),Gf(),e!==null&&Ve(Pa);break;case 24:jn(Be)}}function Hi(e,t){try{var n=t.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var l=a.next;n=l;do{if((n.tag&e)===e){a=void 0;var r=n.create,i=n.inst;a=r(),i.destroy=a}n=n.next}while(n!==l)}}catch(o){ue(t,t.return,o)}}function wa(e,t,n){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var r=l.next;a=r;do{if((a.tag&e)===e){var i=a.inst,o=i.destroy;if(o!==void 0){i.destroy=void 0,l=t;var s=n,u=o;try{u()}catch(d){ue(l,s,d)}}}a=a.next}while(a!==r)}}catch(d){ue(t,t.return,d)}}function Cg(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{_v(t,n)}catch(a){ue(e,e.return,a)}}}function jg(e,t,n){n.props=Wa(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(a){ue(e,t,a)}}function $r(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof n=="function"?e.refCleanup=n(a):n.current=a}}catch(l){ue(e,t,l)}}function en(e,t){var n=e.ref,a=e.refCleanup;if(n!==null)if(typeof a=="function")try{a()}catch(l){ue(e,t,l)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(l){ue(e,t,l)}else n.current=null}function Rg(e){var t=e.type,n=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&a.focus();break e;case"img":n.src?a.src=n.src:n.srcSet&&(a.srcset=n.srcSet)}}catch(l){ue(e,e.return,l)}}function Dc(e,t,n){try{var a=e.stateNode;bS(a,e.type,n,t),a[ot]=t}catch(l){ue(e,e.return,l)}}function Og(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Ca(e.type)||e.tag===4}function _c(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Og(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Ca(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Lu(e,t,n){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=qs));else if(a!==4&&(a===27&&Ca(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(Lu(e,t,n),e=e.sibling;e!==null;)Lu(e,t,n),e=e.sibling}function es(e,t,n){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(a!==4&&(a===27&&Ca(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(es(e,t,n),e=e.sibling;e!==null;)es(e,t,n),e=e.sibling}function Mg(e){var t=e.stateNode,n=e.memoizedProps;try{for(var a=e.type,l=t.attributes;l.length;)t.removeAttributeNode(l[0]);Pe(t,a,n),t[Fe]=e,t[ot]=n}catch(r){ue(e,e.return,r)}}var bn=!1,Me=!1,zc=!1,Fh=typeof WeakSet=="function"?WeakSet:Set,qe=null;function I1(e,t){if(e=e.containerInfo,Qu=us,e=yv(e),Df(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var a=n.getSelection&&n.getSelection();if(a&&a.rangeCount!==0){n=a.anchorNode;var l=a.anchorOffset,r=a.focusNode;a=a.focusOffset;try{n.nodeType,r.nodeType}catch{n=null;break e}var i=0,o=-1,s=-1,u=0,d=0,m=e,h=null;t:for(;;){for(var f;m!==n||l!==0&&m.nodeType!==3||(o=i+l),m!==r||a!==0&&m.nodeType!==3||(s=i+a),m.nodeType===3&&(i+=m.nodeValue.length),(f=m.firstChild)!==null;)h=m,m=f;for(;;){if(m===e)break t;if(h===n&&++u===l&&(o=i),h===r&&++d===a&&(s=i),(f=m.nextSibling)!==null)break;m=h,h=m.parentNode}m=f}n=o===-1||s===-1?null:{start:o,end:s}}else n=null}n=n||{start:0,end:0}}else n=null;for(Xu={focusedElem:e,selectionRange:n},us=!1,qe=t;qe!==null;)if(t=qe,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,qe=e;else for(;qe!==null;){switch(t=qe,r=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if(e&1024&&r!==null){e=void 0,n=t,l=r.memoizedProps,r=r.memoizedState,a=n.stateNode;try{var w=Wa(n.type,l,n.elementType===n.type);e=a.getSnapshotBeforeUpdate(w,r),a.__reactInternalSnapshotBeforeUpdate=e}catch(y){ue(n,n.return,y)}}break;case 3:if(e&1024){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)Zu(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Zu(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if(e&1024)throw Error(j(163))}if(e=t.sibling,e!==null){e.return=t.return,qe=e;break}qe=t.return}}function Dg(e,t,n){var a=n.flags;switch(n.tag){case 0:case 11:case 15:Xn(e,n),a&4&&Hi(5,n);break;case 1:if(Xn(e,n),a&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(i){ue(n,n.return,i)}else{var l=Wa(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(l,t,e.__reactInternalSnapshotBeforeUpdate)}catch(i){ue(n,n.return,i)}}a&64&&Cg(n),a&512&&$r(n,n.return);break;case 3:if(Xn(e,n),a&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{_v(e,t)}catch(i){ue(n,n.return,i)}}break;case 27:t===null&&a&4&&Mg(n);case 26:case 5:Xn(e,n),t===null&&a&4&&Rg(n),a&512&&$r(n,n.return);break;case 12:Xn(e,n);break;case 13:Xn(e,n),a&4&&Ug(e,n),a&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=sS.bind(null,n),NS(e,n))));break;case 22:if(a=n.memoizedState!==null||bn,!a){t=t!==null&&t.memoizedState!==null||Me,l=bn;var r=Me;bn=a,(Me=t)&&!r?Kn(e,n,(n.subtreeFlags&8772)!==0):Xn(e,n),bn=l,Me=r}break;case 30:break;default:Xn(e,n)}}function _g(e){var t=e.alternate;t!==null&&(e.alternate=null,_g(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Nf(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var ge=null,at=!1;function pn(e,t,n){for(n=n.child;n!==null;)zg(e,t,n),n=n.sibling}function zg(e,t,n){if(pt&&typeof pt.onCommitFiberUnmount=="function")try{pt.onCommitFiberUnmount(Ci,n)}catch{}switch(n.tag){case 26:Me||en(n,t),pn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Me||en(n,t);var a=ge,l=at;Ca(n.type)&&(ge=n.stateNode,at=!1),pn(e,t,n),ti(n.stateNode),ge=a,at=l;break;case 5:Me||en(n,t);case 6:if(a=ge,l=at,ge=null,pn(e,t,n),ge=a,at=l,ge!==null)if(at)try{(ge.nodeType===9?ge.body:ge.nodeName==="HTML"?ge.ownerDocument.body:ge).removeChild(n.stateNode)}catch(r){ue(n,t,r)}else try{ge.removeChild(n.stateNode)}catch(r){ue(n,t,r)}break;case 18:ge!==null&&(at?(e=ge,om(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),vi(e)):om(ge,n.stateNode));break;case 4:a=ge,l=at,ge=n.stateNode.containerInfo,at=!0,pn(e,t,n),ge=a,at=l;break;case 0:case 11:case 14:case 15:Me||wa(2,n,t),Me||wa(4,n,t),pn(e,t,n);break;case 1:Me||(en(n,t),a=n.stateNode,typeof a.componentWillUnmount=="function"&&jg(n,t,a)),pn(e,t,n);break;case 21:pn(e,t,n);break;case 22:Me=(a=Me)||n.memoizedState!==null,pn(e,t,n),Me=a;break;default:pn(e,t,n)}}function Ug(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{vi(e)}catch(n){ue(t,t.return,n)}}function eS(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Fh),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Fh),t;default:throw Error(j(435,e.tag))}}function Uc(e,t){var n=eS(e);t.forEach(function(a){var l=cS.bind(null,e,a);n.has(a)||(n.add(a),a.then(l,l))})}function ut(e,t){var n=t.deletions;if(n!==null)for(var a=0;a<n.length;a++){var l=n[a],r=e,i=t,o=i;e:for(;o!==null;){switch(o.tag){case 27:if(Ca(o.type)){ge=o.stateNode,at=!1;break e}break;case 5:ge=o.stateNode,at=!1;break e;case 3:case 4:ge=o.stateNode.containerInfo,at=!0;break e}o=o.return}if(ge===null)throw Error(j(160));zg(r,i,l),ge=null,at=!1,r=l.alternate,r!==null&&(r.return=null),l.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Lg(t,e),t=t.sibling}var Gt=null;function Lg(e,t){var n=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:ut(t,e),ft(e),a&4&&(wa(3,e,e.return),Hi(3,e),wa(5,e,e.return));break;case 1:ut(t,e),ft(e),a&512&&(Me||n===null||en(n,n.return)),a&64&&bn&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?a:n.concat(a))));break;case 26:var l=Gt;if(ut(t,e),ft(e),a&512&&(Me||n===null||en(n,n.return)),a&4){var r=n!==null?n.memoizedState:null;if(a=e.memoizedState,n===null)if(a===null)if(e.stateNode===null){e:{a=e.type,n=e.memoizedProps,l=l.ownerDocument||l;t:switch(a){case"title":r=l.getElementsByTagName("title")[0],(!r||r[Oi]||r[Fe]||r.namespaceURI==="http://www.w3.org/2000/svg"||r.hasAttribute("itemprop"))&&(r=l.createElement(a),l.head.insertBefore(r,l.querySelector("head > title"))),Pe(r,a,n),r[Fe]=e,Ge(r),a=r;break e;case"link":var i=dm("link","href",l).get(a+(n.href||""));if(i){for(var o=0;o<i.length;o++)if(r=i[o],r.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&r.getAttribute("rel")===(n.rel==null?null:n.rel)&&r.getAttribute("title")===(n.title==null?null:n.title)&&r.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){i.splice(o,1);break t}}r=l.createElement(a),Pe(r,a,n),l.head.appendChild(r);break;case"meta":if(i=dm("meta","content",l).get(a+(n.content||""))){for(o=0;o<i.length;o++)if(r=i[o],r.getAttribute("content")===(n.content==null?null:""+n.content)&&r.getAttribute("name")===(n.name==null?null:n.name)&&r.getAttribute("property")===(n.property==null?null:n.property)&&r.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&r.getAttribute("charset")===(n.charSet==null?null:n.charSet)){i.splice(o,1);break t}}r=l.createElement(a),Pe(r,a,n),l.head.appendChild(r);break;default:throw Error(j(468,a))}r[Fe]=e,Ge(r),a=r}e.stateNode=a}else hm(l,e.type,e.stateNode);else e.stateNode=fm(l,a,e.memoizedProps);else r!==a?(r===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):r.count--,a===null?hm(l,e.type,e.stateNode):fm(l,a,e.memoizedProps)):a===null&&e.stateNode!==null&&Dc(e,e.memoizedProps,n.memoizedProps)}break;case 27:ut(t,e),ft(e),a&512&&(Me||n===null||en(n,n.return)),n!==null&&a&4&&Dc(e,e.memoizedProps,n.memoizedProps);break;case 5:if(ut(t,e),ft(e),a&512&&(Me||n===null||en(n,n.return)),e.flags&32){l=e.stateNode;try{tr(l,"")}catch(f){ue(e,e.return,f)}}a&4&&e.stateNode!=null&&(l=e.memoizedProps,Dc(e,l,n!==null?n.memoizedProps:l)),a&1024&&(zc=!0);break;case 6:if(ut(t,e),ft(e),a&4){if(e.stateNode===null)throw Error(j(162));a=e.memoizedProps,n=e.stateNode;try{n.nodeValue=a}catch(f){ue(e,e.return,f)}}break;case 3:if(Ro=null,l=Gt,Gt=os(t.containerInfo),ut(t,e),Gt=l,ft(e),a&4&&n!==null&&n.memoizedState.isDehydrated)try{vi(t.containerInfo)}catch(f){ue(e,e.return,f)}zc&&(zc=!1,Bg(e));break;case 4:a=Gt,Gt=os(e.stateNode.containerInfo),ut(t,e),ft(e),Gt=a;break;case 12:ut(t,e),ft(e);break;case 13:ut(t,e),ft(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(rd=an()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,Uc(e,a)));break;case 22:l=e.memoizedState!==null;var s=n!==null&&n.memoizedState!==null,u=bn,d=Me;if(bn=u||l,Me=d||s,ut(t,e),Me=d,bn=u,ft(e),a&8192)e:for(t=e.stateNode,t._visibility=l?t._visibility&-2:t._visibility|1,l&&(n===null||s||bn||Me||za(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){s=n=t;try{if(r=s.stateNode,l)i=r.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none";else{o=s.stateNode;var m=s.memoizedProps.style,h=m!=null&&m.hasOwnProperty("display")?m.display:null;o.style.display=h==null||typeof h=="boolean"?"":(""+h).trim()}}catch(f){ue(s,s.return,f)}}}else if(t.tag===6){if(n===null){s=t;try{s.stateNode.nodeValue=l?"":s.memoizedProps}catch(f){ue(s,s.return,f)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(n=a.retryQueue,n!==null&&(a.retryQueue=null,Uc(e,n))));break;case 19:ut(t,e),ft(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,Uc(e,a)));break;case 30:break;case 21:break;default:ut(t,e),ft(e)}}function ft(e){var t=e.flags;if(t&2){try{for(var n,a=e.return;a!==null;){if(Og(a)){n=a;break}a=a.return}if(n==null)throw Error(j(160));switch(n.tag){case 27:var l=n.stateNode,r=_c(e);es(e,r,l);break;case 5:var i=n.stateNode;n.flags&32&&(tr(i,""),n.flags&=-33);var o=_c(e);es(e,o,i);break;case 3:case 4:var s=n.stateNode.containerInfo,u=_c(e);Lu(e,u,s);break;default:throw Error(j(161))}}catch(d){ue(e,e.return,d)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Bg(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Bg(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Xn(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Dg(e,t.alternate,t),t=t.sibling}function za(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:wa(4,t,t.return),za(t);break;case 1:en(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&jg(t,t.return,n),za(t);break;case 27:ti(t.stateNode);case 26:case 5:en(t,t.return),za(t);break;case 22:t.memoizedState===null&&za(t);break;case 30:za(t);break;default:za(t)}e=e.sibling}}function Kn(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,l=e,r=t,i=r.flags;switch(r.tag){case 0:case 11:case 15:Kn(l,r,n),Hi(4,r);break;case 1:if(Kn(l,r,n),a=r,l=a.stateNode,typeof l.componentDidMount=="function")try{l.componentDidMount()}catch(u){ue(a,a.return,u)}if(a=r,l=a.updateQueue,l!==null){var o=a.stateNode;try{var s=l.shared.hiddenCallbacks;if(s!==null)for(l.shared.hiddenCallbacks=null,l=0;l<s.length;l++)Dv(s[l],o)}catch(u){ue(a,a.return,u)}}n&&i&64&&Cg(r),$r(r,r.return);break;case 27:Mg(r);case 26:case 5:Kn(l,r,n),n&&a===null&&i&4&&Rg(r),$r(r,r.return);break;case 12:Kn(l,r,n);break;case 13:Kn(l,r,n),n&&i&4&&Ug(l,r);break;case 22:r.memoizedState===null&&Kn(l,r,n),$r(r,r.return);break;case 30:break;default:Kn(l,r,n)}t=t.sibling}}function td(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&zi(n))}function nd(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&zi(e))}function Zt(e,t,n,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Hg(e,t,n,a),t=t.sibling}function Hg(e,t,n,a){var l=t.flags;switch(t.tag){case 0:case 11:case 15:Zt(e,t,n,a),l&2048&&Hi(9,t);break;case 1:Zt(e,t,n,a);break;case 3:Zt(e,t,n,a),l&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&zi(e)));break;case 12:if(l&2048){Zt(e,t,n,a),e=t.stateNode;try{var r=t.memoizedProps,i=r.id,o=r.onPostCommit;typeof o=="function"&&o(i,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(s){ue(t,t.return,s)}}else Zt(e,t,n,a);break;case 13:Zt(e,t,n,a);break;case 23:break;case 22:r=t.stateNode,i=t.alternate,t.memoizedState!==null?r._visibility&2?Zt(e,t,n,a):Wr(e,t):r._visibility&2?Zt(e,t,n,a):(r._visibility|=2,bl(e,t,n,a,(t.subtreeFlags&10256)!==0)),l&2048&&td(i,t);break;case 24:Zt(e,t,n,a),l&2048&&nd(t.alternate,t);break;default:Zt(e,t,n,a)}}function bl(e,t,n,a,l){for(l=l&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var r=e,i=t,o=n,s=a,u=i.flags;switch(i.tag){case 0:case 11:case 15:bl(r,i,o,s,l),Hi(8,i);break;case 23:break;case 22:var d=i.stateNode;i.memoizedState!==null?d._visibility&2?bl(r,i,o,s,l):Wr(r,i):(d._visibility|=2,bl(r,i,o,s,l)),l&&u&2048&&td(i.alternate,i);break;case 24:bl(r,i,o,s,l),l&&u&2048&&nd(i.alternate,i);break;default:bl(r,i,o,s,l)}t=t.sibling}}function Wr(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,a=t,l=a.flags;switch(a.tag){case 22:Wr(n,a),l&2048&&td(a.alternate,a);break;case 24:Wr(n,a),l&2048&&nd(a.alternate,a);break;default:Wr(n,a)}t=t.sibling}}var Hr=8192;function dl(e){if(e.subtreeFlags&Hr)for(e=e.child;e!==null;)kg(e),e=e.sibling}function kg(e){switch(e.tag){case 26:dl(e),e.flags&Hr&&e.memoizedState!==null&&kS(Gt,e.memoizedState,e.memoizedProps);break;case 5:dl(e);break;case 3:case 4:var t=Gt;Gt=os(e.stateNode.containerInfo),dl(e),Gt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Hr,Hr=16777216,dl(e),Hr=t):dl(e));break;default:dl(e)}}function qg(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Mr(e){var t=e.deletions;if(e.flags&16){if(t!==null)for(var n=0;n<t.length;n++){var a=t[n];qe=a,Yg(a,e)}qg(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Gg(e),e=e.sibling}function Gg(e){switch(e.tag){case 0:case 11:case 15:Mr(e),e.flags&2048&&wa(9,e,e.return);break;case 3:Mr(e);break;case 12:Mr(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Co(e)):Mr(e);break;default:Mr(e)}}function Co(e){var t=e.deletions;if(e.flags&16){if(t!==null)for(var n=0;n<t.length;n++){var a=t[n];qe=a,Yg(a,e)}qg(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:wa(8,t,t.return),Co(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,Co(t));break;default:Co(t)}e=e.sibling}}function Yg(e,t){for(;qe!==null;){var n=qe;switch(n.tag){case 0:case 11:case 15:wa(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var a=n.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:zi(n.memoizedState.cache)}if(a=n.child,a!==null)a.return=n,qe=a;else e:for(n=e;qe!==null;){a=qe;var l=a.sibling,r=a.return;if(_g(a),a===n){qe=null;break e}if(l!==null){l.return=r,qe=l;break e}qe=r}}}var tS={getCacheForType:function(e){var t=Je(Be),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},nS=typeof WeakMap=="function"?WeakMap:Map,re=0,fe=null,P=null,F=0,le=0,dt=null,ua=!1,gr=!1,ad=!1,Dn=0,je=0,Ea=0,Za=0,ld=0,Ot=0,ir=0,Ir=null,lt=null,Bu=!1,rd=0,ts=1/0,ns=null,va=null,Xe=0,ga=null,or=null,ql=0,Hu=0,ku=null,Vg=null,ei=0,qu=null;function gt(){if(re&2&&F!==0)return F&-F;if(H.T!==null){var e=nr;return e!==0?e:od()}return ev()}function Qg(){Ot===0&&(Ot=!(F&536870912)||ee?Jp():536870912);var e=Mt.current;return e!==null&&(e.flags|=32),Ot}function yt(e,t,n){(e===fe&&(le===2||le===9)||e.cancelPendingCommit!==null)&&(sr(e,0),fa(e,F,Ot,!1)),Ri(e,n),(!(re&2)||e!==fe)&&(e===fe&&(!(re&2)&&(Za|=n),je===4&&fa(e,F,Ot,!1)),cn(e))}function Xg(e,t,n){if(re&6)throw Error(j(327));var a=!n&&(t&124)===0&&(t&e.expiredLanes)===0||ji(e,t),l=a?rS(e,t):Lc(e,t,!0),r=a;do{if(l===0){gr&&!a&&fa(e,t,0,!1);break}else{if(n=e.current.alternate,r&&!aS(n)){l=Lc(e,t,!1),r=!1;continue}if(l===2){if(r=t,e.errorRecoveryDisabledLanes&r)var i=0;else i=e.pendingLanes&-536870913,i=i!==0?i:i&536870912?536870912:0;if(i!==0){t=i;e:{var o=e;l=Ir;var s=o.current.memoizedState.isDehydrated;if(s&&(sr(o,i).flags|=256),i=Lc(o,i,!1),i!==2){if(ad&&!s){o.errorRecoveryDisabledLanes|=r,Za|=r,l=4;break e}r=lt,lt=l,r!==null&&(lt===null?lt=r:lt.push.apply(lt,r))}l=i}if(r=!1,l!==2)continue}}if(l===1){sr(e,0),fa(e,t,0,!0);break}e:{switch(a=e,r=l,r){case 0:case 1:throw Error(j(345));case 4:if((t&4194048)!==t)break;case 6:fa(a,t,Ot,!ua);break e;case 2:lt=null;break;case 3:case 5:break;default:throw Error(j(329))}if((t&62914560)===t&&(l=rd+300-an(),10<l)){if(fa(a,t,Ot,!ua),Cs(a,0,!0)!==0)break e;a.timeoutHandle=uy(Jh.bind(null,a,n,lt,ns,Bu,t,Ot,Za,ir,ua,r,2,-0,0),l);break e}Jh(a,n,lt,ns,Bu,t,Ot,Za,ir,ua,r,0,-0,0)}}break}while(!0);cn(e)}function Jh(e,t,n,a,l,r,i,o,s,u,d,m,h,f){if(e.timeoutHandle=-1,m=t.subtreeFlags,(m&8192||(m&16785408)===16785408)&&(di={stylesheets:null,count:0,unsuspend:HS},kg(t),m=qS(),m!==null)){e.cancelPendingCommit=m(Wh.bind(null,e,t,r,n,a,l,i,o,s,d,1,h,f)),fa(e,r,i,!u);return}Wh(e,t,r,n,a,l,i,o,s)}function aS(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var a=0;a<n.length;a++){var l=n[a],r=l.getSnapshot;l=l.value;try{if(!xt(r(),l))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function fa(e,t,n,a){t&=~ld,t&=~Za,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var l=t;0<l;){var r=31-vt(l),i=1<<r;a[r]=-1,l&=~i}n!==0&&Wp(e,n,t)}function Bs(){return re&6?!0:(ki(0),!1)}function id(){if(P!==null){if(le===0)var e=P.return;else e=P,Tn=ll=null,Pf(e),kl=null,ci=0,e=P;for(;e!==null;)Ng(e.alternate,e),e=e.return;P=null}}function sr(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,SS(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),id(),fe=e,P=n=Cn(e.current,null),F=t,le=0,dt=null,ua=!1,gr=ji(e,t),ad=!1,ir=Ot=ld=Za=Ea=je=0,lt=Ir=null,Bu=!1,t&8&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var l=31-vt(a),r=1<<l;t|=e[l],a&=~r}return Dn=t,Ms(),n}function Pg(e,t){Q=null,H.H=Fo,t===Ui||t===_s?(t=jh(),le=3):t===Ov?(t=jh(),le=4):le=t===xg?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,dt=t,P===null&&(je=1,Wo(e,Rt(t,e.current)))}function Zg(){var e=H.H;return H.H=Fo,e===null?Fo:e}function Kg(){var e=H.A;return H.A=tS,e}function Gu(){je=4,ua||(F&4194048)!==F&&Mt.current!==null||(gr=!0),!(Ea&134217727)&&!(Za&134217727)||fe===null||fa(fe,F,Ot,!1)}function Lc(e,t,n){var a=re;re|=2;var l=Zg(),r=Kg();(fe!==e||F!==t)&&(ns=null,sr(e,t)),t=!1;var i=je;e:do try{if(le!==0&&P!==null){var o=P,s=dt;switch(le){case 8:id(),i=6;break e;case 3:case 2:case 9:case 6:Mt.current===null&&(t=!0);var u=le;if(le=0,dt=null,Dl(e,o,s,u),n&&gr){i=0;break e}break;default:u=le,le=0,dt=null,Dl(e,o,s,u)}}lS(),i=je;break}catch(d){Pg(e,d)}while(!0);return t&&e.shellSuspendCounter++,Tn=ll=null,re=a,H.H=l,H.A=r,P===null&&(fe=null,F=0,Ms()),i}function lS(){for(;P!==null;)Fg(P)}function rS(e,t){var n=re;re|=2;var a=Zg(),l=Kg();fe!==e||F!==t?(ns=null,ts=an()+500,sr(e,t)):gr=ji(e,t);e:do try{if(le!==0&&P!==null){t=P;var r=dt;t:switch(le){case 1:le=0,dt=null,Dl(e,t,r,1);break;case 2:case 9:if(Ch(r)){le=0,dt=null,$h(t);break}t=function(){le!==2&&le!==9||fe!==e||(le=7),cn(e)},r.then(t,t);break e;case 3:le=7;break e;case 4:le=5;break e;case 7:Ch(r)?(le=0,dt=null,$h(t)):(le=0,dt=null,Dl(e,t,r,7));break;case 5:var i=null;switch(P.tag){case 26:i=P.memoizedState;case 5:case 27:var o=P;if(!i||my(i)){le=0,dt=null;var s=o.sibling;if(s!==null)P=s;else{var u=o.return;u!==null?(P=u,Hs(u)):P=null}break t}}le=0,dt=null,Dl(e,t,r,5);break;case 6:le=0,dt=null,Dl(e,t,r,6);break;case 8:id(),je=6;break e;default:throw Error(j(462))}}iS();break}catch(d){Pg(e,d)}while(!0);return Tn=ll=null,H.H=a,H.A=l,re=n,P!==null?0:(fe=null,F=0,Ms(),je)}function iS(){for(;P!==null&&!jx();)Fg(P)}function Fg(e){var t=Tg(e.alternate,e,Dn);e.memoizedProps=e.pendingProps,t===null?Hs(e):P=t}function $h(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Qh(n,t,t.pendingProps,t.type,void 0,F);break;case 11:t=Qh(n,t,t.pendingProps,t.type.render,t.ref,F);break;case 5:Pf(t);default:Ng(n,t),t=P=Nv(t,Dn),t=Tg(n,t,Dn)}e.memoizedProps=e.pendingProps,t===null?Hs(e):P=t}function Dl(e,t,n,a){Tn=ll=null,Pf(t),kl=null,ci=0;var l=t.return;try{if(F1(e,l,t,n,F)){je=1,Wo(e,Rt(n,e.current)),P=null;return}}catch(r){if(l!==null)throw P=l,r;je=1,Wo(e,Rt(n,e.current)),P=null;return}t.flags&32768?(ee||a===1?e=!0:gr||F&536870912?e=!1:(ua=e=!0,(a===2||a===9||a===3||a===6)&&(a=Mt.current,a!==null&&a.tag===13&&(a.flags|=16384))),Jg(t,e)):Hs(t)}function Hs(e){var t=e;do{if(t.flags&32768){Jg(t,ua);return}e=t.return;var n=$1(t.alternate,t,Dn);if(n!==null){P=n;return}if(t=t.sibling,t!==null){P=t;return}P=t=e}while(t!==null);je===0&&(je=5)}function Jg(e,t){do{var n=W1(e.alternate,e);if(n!==null){n.flags&=32767,P=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){P=e;return}P=e=n}while(e!==null);je=6,P=null}function Wh(e,t,n,a,l,r,i,o,s){e.cancelPendingCommit=null;do ks();while(Xe!==0);if(re&6)throw Error(j(327));if(t!==null){if(t===e.current)throw Error(j(177));if(r=t.lanes|t.childLanes,r|=_f,Hx(e,n,r,i,o,s),e===fe&&(P=fe=null,F=0),or=t,ga=e,ql=n,Hu=r,ku=l,Vg=a,t.subtreeFlags&10256||t.flags&10256?(e.callbackNode=null,e.callbackPriority=0,uS(ko,function(){return ty(),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,t.subtreeFlags&13878||a){a=H.T,H.T=null,l=te.p,te.p=2,i=re,re|=4;try{I1(e,t,n)}finally{re=i,te.p=l,H.T=a}}Xe=1,$g(),Wg(),Ig()}}function $g(){if(Xe===1){Xe=0;var e=ga,t=or,n=(t.flags&13878)!==0;if(t.subtreeFlags&13878||n){n=H.T,H.T=null;var a=te.p;te.p=2;var l=re;re|=4;try{Lg(t,e);var r=Xu,i=yv(e.containerInfo),o=r.focusedElem,s=r.selectionRange;if(i!==o&&o&&o.ownerDocument&&gv(o.ownerDocument.documentElement,o)){if(s!==null&&Df(o)){var u=s.start,d=s.end;if(d===void 0&&(d=u),"selectionStart"in o)o.selectionStart=u,o.selectionEnd=Math.min(d,o.value.length);else{var m=o.ownerDocument||document,h=m&&m.defaultView||window;if(h.getSelection){var f=h.getSelection(),w=o.textContent.length,y=Math.min(s.start,w),x=s.end===void 0?y:Math.min(s.end,w);!f.extend&&y>x&&(i=x,x=y,y=i);var v=bh(o,y),g=bh(o,x);if(v&&g&&(f.rangeCount!==1||f.anchorNode!==v.node||f.anchorOffset!==v.offset||f.focusNode!==g.node||f.focusOffset!==g.offset)){var b=m.createRange();b.setStart(v.node,v.offset),f.removeAllRanges(),y>x?(f.addRange(b),f.extend(g.node,g.offset)):(b.setEnd(g.node,g.offset),f.addRange(b))}}}}for(m=[],f=o;f=f.parentNode;)f.nodeType===1&&m.push({element:f,left:f.scrollLeft,top:f.scrollTop});for(typeof o.focus=="function"&&o.focus(),o=0;o<m.length;o++){var S=m[o];S.element.scrollLeft=S.left,S.element.scrollTop=S.top}}us=!!Qu,Xu=Qu=null}finally{re=l,te.p=a,H.T=n}}e.current=t,Xe=2}}function Wg(){if(Xe===2){Xe=0;var e=ga,t=or,n=(t.flags&8772)!==0;if(t.subtreeFlags&8772||n){n=H.T,H.T=null;var a=te.p;te.p=2;var l=re;re|=4;try{Dg(e,t.alternate,t)}finally{re=l,te.p=a,H.T=n}}Xe=3}}function Ig(){if(Xe===4||Xe===3){Xe=0,Rx();var e=ga,t=or,n=ql,a=Vg;t.subtreeFlags&10256||t.flags&10256?Xe=5:(Xe=0,or=ga=null,ey(e,e.pendingLanes));var l=e.pendingLanes;if(l===0&&(va=null),Tf(n),t=t.stateNode,pt&&typeof pt.onCommitFiberRoot=="function")try{pt.onCommitFiberRoot(Ci,t,void 0,(t.current.flags&128)===128)}catch{}if(a!==null){t=H.T,l=te.p,te.p=2,H.T=null;try{for(var r=e.onRecoverableError,i=0;i<a.length;i++){var o=a[i];r(o.value,{componentStack:o.stack})}}finally{H.T=t,te.p=l}}ql&3&&ks(),cn(e),l=e.pendingLanes,n&4194090&&l&42?e===qu?ei++:(ei=0,qu=e):ei=0,ki(0)}}function ey(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,zi(t)))}function ks(e){return $g(),Wg(),Ig(),ty()}function ty(){if(Xe!==5)return!1;var e=ga,t=Hu;Hu=0;var n=Tf(ql),a=H.T,l=te.p;try{te.p=32>n?32:n,H.T=null,n=ku,ku=null;var r=ga,i=ql;if(Xe=0,or=ga=null,ql=0,re&6)throw Error(j(331));var o=re;if(re|=4,Gg(r.current),Hg(r,r.current,i,n),re=o,ki(0,!1),pt&&typeof pt.onPostCommitFiberRoot=="function")try{pt.onPostCommitFiberRoot(Ci,r)}catch{}return!0}finally{te.p=l,H.T=a,ey(e,t)}}function Ih(e,t,n){t=Rt(n,t),t=_u(e.stateNode,t,2),e=pa(e,t,2),e!==null&&(Ri(e,2),cn(e))}function ue(e,t,n){if(e.tag===3)Ih(e,e,n);else for(;t!==null;){if(t.tag===3){Ih(t,e,n);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(va===null||!va.has(a))){e=Rt(n,e),n=yg(2),a=pa(t,n,2),a!==null&&(bg(n,a,t,e),Ri(a,2),cn(a));break}}t=t.return}}function Bc(e,t,n){var a=e.pingCache;if(a===null){a=e.pingCache=new nS;var l=new Set;a.set(t,l)}else l=a.get(t),l===void 0&&(l=new Set,a.set(t,l));l.has(n)||(ad=!0,l.add(n),e=oS.bind(null,e,t,n),t.then(e,e))}function oS(e,t,n){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,fe===e&&(F&n)===n&&(je===4||je===3&&(F&62914560)===F&&300>an()-rd?!(re&2)&&sr(e,0):ld|=n,ir===F&&(ir=0)),cn(e)}function ny(e,t){t===0&&(t=$p()),e=vr(e,t),e!==null&&(Ri(e,t),cn(e))}function sS(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),ny(e,n)}function cS(e,t){var n=0;switch(e.tag){case 13:var a=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(j(314))}a!==null&&a.delete(t),ny(e,n)}function uS(e,t){return Ef(e,t)}var as=null,xl=null,Yu=!1,ls=!1,Hc=!1,Ka=0;function cn(e){e!==xl&&e.next===null&&(xl===null?as=xl=e:xl=xl.next=e),ls=!0,Yu||(Yu=!0,dS())}function ki(e,t){if(!Hc&&ls){Hc=!0;do for(var n=!1,a=as;a!==null;){if(e!==0){var l=a.pendingLanes;if(l===0)var r=0;else{var i=a.suspendedLanes,o=a.pingedLanes;r=(1<<31-vt(42|e)+1)-1,r&=l&~(i&~o),r=r&201326741?r&201326741|1:r?r|2:0}r!==0&&(n=!0,em(a,r))}else r=F,r=Cs(a,a===fe?r:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),!(r&3)||ji(a,r)||(n=!0,em(a,r));a=a.next}while(n);Hc=!1}}function fS(){ay()}function ay(){ls=Yu=!1;var e=0;Ka!==0&&(xS()&&(e=Ka),Ka=0);for(var t=an(),n=null,a=as;a!==null;){var l=a.next,r=ly(a,t);r===0?(a.next=null,n===null?as=l:n.next=l,l===null&&(xl=n)):(n=a,(e!==0||r&3)&&(ls=!0)),a=l}ki(e)}function ly(e,t){for(var n=e.suspendedLanes,a=e.pingedLanes,l=e.expirationTimes,r=e.pendingLanes&-62914561;0<r;){var i=31-vt(r),o=1<<i,s=l[i];s===-1?(!(o&n)||o&a)&&(l[i]=Bx(o,t)):s<=t&&(e.expiredLanes|=o),r&=~o}if(t=fe,n=F,n=Cs(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,n===0||e===t&&(le===2||le===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&uc(a),e.callbackNode=null,e.callbackPriority=0;if(!(n&3)||ji(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(a!==null&&uc(a),Tf(n)){case 2:case 8:n=Kp;break;case 32:n=ko;break;case 268435456:n=Fp;break;default:n=ko}return a=ry.bind(null,e),n=Ef(n,a),e.callbackPriority=t,e.callbackNode=n,t}return a!==null&&a!==null&&uc(a),e.callbackPriority=2,e.callbackNode=null,2}function ry(e,t){if(Xe!==0&&Xe!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(ks()&&e.callbackNode!==n)return null;var a=F;return a=Cs(e,e===fe?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(Xg(e,a,t),ly(e,an()),e.callbackNode!=null&&e.callbackNode===n?ry.bind(null,e):null)}function em(e,t){if(ks())return null;Xg(e,t,!0)}function dS(){wS(function(){re&6?Ef(Zp,fS):ay()})}function od(){return Ka===0&&(Ka=Jp()),Ka}function tm(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:bo(""+e)}function nm(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function hS(e,t,n,a,l){if(t==="submit"&&n&&n.stateNode===l){var r=tm((l[ot]||null).action),i=a.submitter;i&&(t=(t=i[ot]||null)?tm(t.formAction):i.getAttribute("formAction"),t!==null&&(r=t,i=null));var o=new js("action","action",null,a,l);e.push({event:o,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(Ka!==0){var s=i?nm(l,i):new FormData(l);Mu(n,{pending:!0,data:s,method:l.method,action:r},null,s)}}else typeof r=="function"&&(o.preventDefault(),s=i?nm(l,i):new FormData(l),Mu(n,{pending:!0,data:s,method:l.method,action:r},r,s))},currentTarget:l}]})}}for(var kc=0;kc<bu.length;kc++){var qc=bu[kc],mS=qc.toLowerCase(),pS=qc[0].toUpperCase()+qc.slice(1);Xt(mS,"on"+pS)}Xt(xv,"onAnimationEnd");Xt(Sv,"onAnimationIteration");Xt(wv,"onAnimationStart");Xt("dblclick","onDoubleClick");Xt("focusin","onFocus");Xt("focusout","onBlur");Xt(D1,"onTransitionRun");Xt(_1,"onTransitionStart");Xt(z1,"onTransitionCancel");Xt(Ev,"onTransitionEnd");er("onMouseEnter",["mouseout","mouseover"]);er("onMouseLeave",["mouseout","mouseover"]);er("onPointerEnter",["pointerout","pointerover"]);er("onPointerLeave",["pointerout","pointerover"]);tl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));tl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));tl("onBeforeInput",["compositionend","keypress","textInput","paste"]);tl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));tl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));tl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ui="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),vS=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(ui));function iy(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var a=e[n],l=a.event;a=a.listeners;e:{var r=void 0;if(t)for(var i=a.length-1;0<=i;i--){var o=a[i],s=o.instance,u=o.currentTarget;if(o=o.listener,s!==r&&l.isPropagationStopped())break e;r=o,l.currentTarget=u;try{r(l)}catch(d){$o(d)}l.currentTarget=null,r=s}else for(i=0;i<a.length;i++){if(o=a[i],s=o.instance,u=o.currentTarget,o=o.listener,s!==r&&l.isPropagationStopped())break e;r=o,l.currentTarget=u;try{r(l)}catch(d){$o(d)}l.currentTarget=null,r=s}}}}function X(e,t){var n=t[du];n===void 0&&(n=t[du]=new Set);var a=e+"__bubble";n.has(a)||(oy(t,e,2,!1),n.add(a))}function Gc(e,t,n){var a=0;t&&(a|=4),oy(n,e,a,t)}var ro="_reactListening"+Math.random().toString(36).slice(2);function sd(e){if(!e[ro]){e[ro]=!0,tv.forEach(function(n){n!=="selectionchange"&&(vS.has(n)||Gc(n,!1,e),Gc(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ro]||(t[ro]=!0,Gc("selectionchange",!1,t))}}function oy(e,t,n,a){switch(by(t)){case 2:var l=VS;break;case 8:l=QS;break;default:l=dd}n=l.bind(null,t,n,e),l=void 0,!vu||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),a?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function Yc(e,t,n,a,l){var r=a;if(!(t&1)&&!(t&2)&&a!==null)e:for(;;){if(a===null)return;var i=a.tag;if(i===3||i===4){var o=a.stateNode.containerInfo;if(o===l)break;if(i===4)for(i=a.return;i!==null;){var s=i.tag;if((s===3||s===4)&&i.stateNode.containerInfo===l)return;i=i.return}for(;o!==null;){if(i=El(o),i===null)return;if(s=i.tag,s===5||s===6||s===26||s===27){a=r=i;continue e}o=o.parentNode}}a=a.return}cv(function(){var u=r,d=jf(n),m=[];e:{var h=Av.get(e);if(h!==void 0){var f=js,w=e;switch(e){case"keypress":if(So(n)===0)break e;case"keydown":case"keyup":f=u1;break;case"focusin":w="focus",f=yc;break;case"focusout":w="blur",f=yc;break;case"beforeblur":case"afterblur":f=yc;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":f=ch;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":f=Wx;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":f=h1;break;case xv:case Sv:case wv:f=t1;break;case Ev:f=p1;break;case"scroll":case"scrollend":f=Jx;break;case"wheel":f=g1;break;case"copy":case"cut":case"paste":f=a1;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":f=fh;break;case"toggle":case"beforetoggle":f=b1}var y=(t&4)!==0,x=!y&&(e==="scroll"||e==="scrollend"),v=y?h!==null?h+"Capture":null:h;y=[];for(var g=u,b;g!==null;){var S=g;if(b=S.stateNode,S=S.tag,S!==5&&S!==26&&S!==27||b===null||v===null||(S=li(g,v),S!=null&&y.push(fi(g,S,b))),x)break;g=g.return}0<y.length&&(h=new f(h,w,null,n,d),m.push({event:h,listeners:y}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",f=e==="mouseout"||e==="pointerout",h&&n!==pu&&(w=n.relatedTarget||n.fromElement)&&(El(w)||w[mr]))break e;if((f||h)&&(h=d.window===d?d:(h=d.ownerDocument)?h.defaultView||h.parentWindow:window,f?(w=n.relatedTarget||n.toElement,f=u,w=w?El(w):null,w!==null&&(x=Ni(w),y=w.tag,w!==x||y!==5&&y!==27&&y!==6)&&(w=null)):(f=null,w=u),f!==w)){if(y=ch,S="onMouseLeave",v="onMouseEnter",g="mouse",(e==="pointerout"||e==="pointerover")&&(y=fh,S="onPointerLeave",v="onPointerEnter",g="pointer"),x=f==null?h:Br(f),b=w==null?h:Br(w),h=new y(S,g+"leave",f,n,d),h.target=x,h.relatedTarget=b,S=null,El(d)===u&&(y=new y(v,g+"enter",w,n,d),y.target=b,y.relatedTarget=x,S=y),x=S,f&&w)t:{for(y=f,v=w,g=0,b=y;b;b=hl(b))g++;for(b=0,S=v;S;S=hl(S))b++;for(;0<g-b;)y=hl(y),g--;for(;0<b-g;)v=hl(v),b--;for(;g--;){if(y===v||v!==null&&y===v.alternate)break t;y=hl(y),v=hl(v)}y=null}else y=null;f!==null&&am(m,h,f,y,!1),w!==null&&x!==null&&am(m,x,w,y,!0)}}e:{if(h=u?Br(u):window,f=h.nodeName&&h.nodeName.toLowerCase(),f==="select"||f==="input"&&h.type==="file")var E=ph;else if(mh(h))if(pv)E=R1;else{E=C1;var T=N1}else f=h.nodeName,!f||f.toLowerCase()!=="input"||h.type!=="checkbox"&&h.type!=="radio"?u&&Cf(u.elementType)&&(E=ph):E=j1;if(E&&(E=E(e,u))){mv(m,E,n,d);break e}T&&T(e,h,u),e==="focusout"&&u&&h.type==="number"&&u.memoizedProps.value!=null&&mu(h,"number",h.value)}switch(T=u?Br(u):window,e){case"focusin":(mh(T)||T.contentEditable==="true")&&(Nl=T,gu=u,Qr=null);break;case"focusout":Qr=gu=Nl=null;break;case"mousedown":yu=!0;break;case"contextmenu":case"mouseup":case"dragend":yu=!1,xh(m,n,d);break;case"selectionchange":if(M1)break;case"keydown":case"keyup":xh(m,n,d)}var A;if(Mf)e:{switch(e){case"compositionstart":var C="onCompositionStart";break e;case"compositionend":C="onCompositionEnd";break e;case"compositionupdate":C="onCompositionUpdate";break e}C=void 0}else Tl?dv(e,n)&&(C="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(C="onCompositionStart");C&&(fv&&n.locale!=="ko"&&(Tl||C!=="onCompositionStart"?C==="onCompositionEnd"&&Tl&&(A=uv()):(ca=d,Rf="value"in ca?ca.value:ca.textContent,Tl=!0)),T=rs(u,C),0<T.length&&(C=new uh(C,e,null,n,d),m.push({event:C,listeners:T}),A?C.data=A:(A=hv(n),A!==null&&(C.data=A)))),(A=S1?w1(e,n):E1(e,n))&&(C=rs(u,"onBeforeInput"),0<C.length&&(T=new uh("onBeforeInput","beforeinput",null,n,d),m.push({event:T,listeners:C}),T.data=A)),hS(m,e,u,n,d)}iy(m,t)})}function fi(e,t,n){return{instance:e,listener:t,currentTarget:n}}function rs(e,t){for(var n=t+"Capture",a=[];e!==null;){var l=e,r=l.stateNode;if(l=l.tag,l!==5&&l!==26&&l!==27||r===null||(l=li(e,n),l!=null&&a.unshift(fi(e,l,r)),l=li(e,t),l!=null&&a.push(fi(e,l,r))),e.tag===3)return a;e=e.return}return[]}function hl(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function am(e,t,n,a,l){for(var r=t._reactName,i=[];n!==null&&n!==a;){var o=n,s=o.alternate,u=o.stateNode;if(o=o.tag,s!==null&&s===a)break;o!==5&&o!==26&&o!==27||u===null||(s=u,l?(u=li(n,r),u!=null&&i.unshift(fi(n,u,s))):l||(u=li(n,r),u!=null&&i.push(fi(n,u,s)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var gS=/\r\n?/g,yS=/\u0000|\uFFFD/g;function lm(e){return(typeof e=="string"?e:""+e).replace(gS,`
`).replace(yS,"")}function sy(e,t){return t=lm(t),lm(e)===t}function qs(){}function oe(e,t,n,a,l,r){switch(n){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||tr(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&tr(e,""+a);break;case"className":Wi(e,"class",a);break;case"tabIndex":Wi(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":Wi(e,n,a);break;case"style":sv(e,a,r);break;case"data":if(t!=="object"){Wi(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(n);break}a=bo(""+a),e.setAttribute(n,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof r=="function"&&(n==="formAction"?(t!=="input"&&oe(e,t,"name",l.name,l,null),oe(e,t,"formEncType",l.formEncType,l,null),oe(e,t,"formMethod",l.formMethod,l,null),oe(e,t,"formTarget",l.formTarget,l,null)):(oe(e,t,"encType",l.encType,l,null),oe(e,t,"method",l.method,l,null),oe(e,t,"target",l.target,l,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(n);break}a=bo(""+a),e.setAttribute(n,a);break;case"onClick":a!=null&&(e.onclick=qs);break;case"onScroll":a!=null&&X("scroll",e);break;case"onScrollEnd":a!=null&&X("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(j(61));if(n=a.__html,n!=null){if(l.children!=null)throw Error(j(60));e.innerHTML=n}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}n=bo(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,""+a):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":a===!0?e.setAttribute(n,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(n,a):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(n,a):e.removeAttribute(n);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(n):e.setAttribute(n,a);break;case"popover":X("beforetoggle",e),X("toggle",e),yo(e,"popover",a);break;case"xlinkActuate":hn(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":hn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":hn(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":hn(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":hn(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":hn(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":hn(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":hn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":hn(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":yo(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Kx.get(n)||n,yo(e,n,a))}}function Vu(e,t,n,a,l,r){switch(n){case"style":sv(e,a,r);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(j(61));if(n=a.__html,n!=null){if(l.children!=null)throw Error(j(60));e.innerHTML=n}}break;case"children":typeof a=="string"?tr(e,a):(typeof a=="number"||typeof a=="bigint")&&tr(e,""+a);break;case"onScroll":a!=null&&X("scroll",e);break;case"onScrollEnd":a!=null&&X("scrollend",e);break;case"onClick":a!=null&&(e.onclick=qs);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!nv.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(l=n.endsWith("Capture"),t=n.slice(2,l?n.length-7:void 0),r=e[ot]||null,r=r!=null?r[n]:null,typeof r=="function"&&e.removeEventListener(t,r,l),typeof a=="function")){typeof r!="function"&&r!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,a,l);break e}n in e?e[n]=a:a===!0?e.setAttribute(n,""):yo(e,n,a)}}}function Pe(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":X("error",e),X("load",e);var a=!1,l=!1,r;for(r in n)if(n.hasOwnProperty(r)){var i=n[r];if(i!=null)switch(r){case"src":a=!0;break;case"srcSet":l=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(j(137,t));default:oe(e,t,r,i,n,null)}}l&&oe(e,t,"srcSet",n.srcSet,n,null),a&&oe(e,t,"src",n.src,n,null);return;case"input":X("invalid",e);var o=r=i=l=null,s=null,u=null;for(a in n)if(n.hasOwnProperty(a)){var d=n[a];if(d!=null)switch(a){case"name":l=d;break;case"type":i=d;break;case"checked":s=d;break;case"defaultChecked":u=d;break;case"value":r=d;break;case"defaultValue":o=d;break;case"children":case"dangerouslySetInnerHTML":if(d!=null)throw Error(j(137,t));break;default:oe(e,t,a,d,n,null)}}rv(e,r,o,s,u,i,l,!1),qo(e);return;case"select":X("invalid",e),a=i=r=null;for(l in n)if(n.hasOwnProperty(l)&&(o=n[l],o!=null))switch(l){case"value":r=o;break;case"defaultValue":i=o;break;case"multiple":a=o;default:oe(e,t,l,o,n,null)}t=r,n=i,e.multiple=!!a,t!=null?zl(e,!!a,t,!1):n!=null&&zl(e,!!a,n,!0);return;case"textarea":X("invalid",e),r=l=a=null;for(i in n)if(n.hasOwnProperty(i)&&(o=n[i],o!=null))switch(i){case"value":a=o;break;case"defaultValue":l=o;break;case"children":r=o;break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(j(91));break;default:oe(e,t,i,o,n,null)}ov(e,a,l,r),qo(e);return;case"option":for(s in n)if(n.hasOwnProperty(s)&&(a=n[s],a!=null))switch(s){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:oe(e,t,s,a,n,null)}return;case"dialog":X("beforetoggle",e),X("toggle",e),X("cancel",e),X("close",e);break;case"iframe":case"object":X("load",e);break;case"video":case"audio":for(a=0;a<ui.length;a++)X(ui[a],e);break;case"image":X("error",e),X("load",e);break;case"details":X("toggle",e);break;case"embed":case"source":case"link":X("error",e),X("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(u in n)if(n.hasOwnProperty(u)&&(a=n[u],a!=null))switch(u){case"children":case"dangerouslySetInnerHTML":throw Error(j(137,t));default:oe(e,t,u,a,n,null)}return;default:if(Cf(t)){for(d in n)n.hasOwnProperty(d)&&(a=n[d],a!==void 0&&Vu(e,t,d,a,n,void 0));return}}for(o in n)n.hasOwnProperty(o)&&(a=n[o],a!=null&&oe(e,t,o,a,n,null))}function bS(e,t,n,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var l=null,r=null,i=null,o=null,s=null,u=null,d=null;for(f in n){var m=n[f];if(n.hasOwnProperty(f)&&m!=null)switch(f){case"checked":break;case"value":break;case"defaultValue":s=m;default:a.hasOwnProperty(f)||oe(e,t,f,null,a,m)}}for(var h in a){var f=a[h];if(m=n[h],a.hasOwnProperty(h)&&(f!=null||m!=null))switch(h){case"type":r=f;break;case"name":l=f;break;case"checked":u=f;break;case"defaultChecked":d=f;break;case"value":i=f;break;case"defaultValue":o=f;break;case"children":case"dangerouslySetInnerHTML":if(f!=null)throw Error(j(137,t));break;default:f!==m&&oe(e,t,h,f,a,m)}}hu(e,i,o,s,u,d,r,l);return;case"select":f=i=o=h=null;for(r in n)if(s=n[r],n.hasOwnProperty(r)&&s!=null)switch(r){case"value":break;case"multiple":f=s;default:a.hasOwnProperty(r)||oe(e,t,r,null,a,s)}for(l in a)if(r=a[l],s=n[l],a.hasOwnProperty(l)&&(r!=null||s!=null))switch(l){case"value":h=r;break;case"defaultValue":o=r;break;case"multiple":i=r;default:r!==s&&oe(e,t,l,r,a,s)}t=o,n=i,a=f,h!=null?zl(e,!!n,h,!1):!!a!=!!n&&(t!=null?zl(e,!!n,t,!0):zl(e,!!n,n?[]:"",!1));return;case"textarea":f=h=null;for(o in n)if(l=n[o],n.hasOwnProperty(o)&&l!=null&&!a.hasOwnProperty(o))switch(o){case"value":break;case"children":break;default:oe(e,t,o,null,a,l)}for(i in a)if(l=a[i],r=n[i],a.hasOwnProperty(i)&&(l!=null||r!=null))switch(i){case"value":h=l;break;case"defaultValue":f=l;break;case"children":break;case"dangerouslySetInnerHTML":if(l!=null)throw Error(j(91));break;default:l!==r&&oe(e,t,i,l,a,r)}iv(e,h,f);return;case"option":for(var w in n)if(h=n[w],n.hasOwnProperty(w)&&h!=null&&!a.hasOwnProperty(w))switch(w){case"selected":e.selected=!1;break;default:oe(e,t,w,null,a,h)}for(s in a)if(h=a[s],f=n[s],a.hasOwnProperty(s)&&h!==f&&(h!=null||f!=null))switch(s){case"selected":e.selected=h&&typeof h!="function"&&typeof h!="symbol";break;default:oe(e,t,s,h,a,f)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var y in n)h=n[y],n.hasOwnProperty(y)&&h!=null&&!a.hasOwnProperty(y)&&oe(e,t,y,null,a,h);for(u in a)if(h=a[u],f=n[u],a.hasOwnProperty(u)&&h!==f&&(h!=null||f!=null))switch(u){case"children":case"dangerouslySetInnerHTML":if(h!=null)throw Error(j(137,t));break;default:oe(e,t,u,h,a,f)}return;default:if(Cf(t)){for(var x in n)h=n[x],n.hasOwnProperty(x)&&h!==void 0&&!a.hasOwnProperty(x)&&Vu(e,t,x,void 0,a,h);for(d in a)h=a[d],f=n[d],!a.hasOwnProperty(d)||h===f||h===void 0&&f===void 0||Vu(e,t,d,h,a,f);return}}for(var v in n)h=n[v],n.hasOwnProperty(v)&&h!=null&&!a.hasOwnProperty(v)&&oe(e,t,v,null,a,h);for(m in a)h=a[m],f=n[m],!a.hasOwnProperty(m)||h===f||h==null&&f==null||oe(e,t,m,h,a,f)}var Qu=null,Xu=null;function is(e){return e.nodeType===9?e:e.ownerDocument}function rm(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function cy(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Pu(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Vc=null;function xS(){var e=window.event;return e&&e.type==="popstate"?e===Vc?!1:(Vc=e,!0):(Vc=null,!1)}var uy=typeof setTimeout=="function"?setTimeout:void 0,SS=typeof clearTimeout=="function"?clearTimeout:void 0,im=typeof Promise=="function"?Promise:void 0,wS=typeof queueMicrotask=="function"?queueMicrotask:typeof im<"u"?function(e){return im.resolve(null).then(e).catch(ES)}:uy;function ES(e){setTimeout(function(){throw e})}function Ca(e){return e==="head"}function om(e,t){var n=t,a=0,l=0;do{var r=n.nextSibling;if(e.removeChild(n),r&&r.nodeType===8)if(n=r.data,n==="/$"){if(0<a&&8>a){n=a;var i=e.ownerDocument;if(n&1&&ti(i.documentElement),n&2&&ti(i.body),n&4)for(n=i.head,ti(n),i=n.firstChild;i;){var o=i.nextSibling,s=i.nodeName;i[Oi]||s==="SCRIPT"||s==="STYLE"||s==="LINK"&&i.rel.toLowerCase()==="stylesheet"||n.removeChild(i),i=o}}if(l===0){e.removeChild(r),vi(t);return}l--}else n==="$"||n==="$?"||n==="$!"?l++:a=n.charCodeAt(0)-48;else a=0;n=r}while(n);vi(t)}function Zu(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Zu(n),Nf(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function AS(e,t,n,a){for(;e.nodeType===1;){var l=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[Oi])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(r=e.getAttribute("rel"),r==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(r!==l.rel||e.getAttribute("href")!==(l.href==null||l.href===""?null:l.href)||e.getAttribute("crossorigin")!==(l.crossOrigin==null?null:l.crossOrigin)||e.getAttribute("title")!==(l.title==null?null:l.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(r=e.getAttribute("src"),(r!==(l.src==null?null:l.src)||e.getAttribute("type")!==(l.type==null?null:l.type)||e.getAttribute("crossorigin")!==(l.crossOrigin==null?null:l.crossOrigin))&&r&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var r=l.name==null?null:""+l.name;if(l.type==="hidden"&&e.getAttribute("name")===r)return e}else return e;if(e=Yt(e.nextSibling),e===null)break}return null}function TS(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Yt(e.nextSibling),e===null))return null;return e}function Ku(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function NS(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var a=function(){t(),n.removeEventListener("DOMContentLoaded",a)};n.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function Yt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Fu=null;function sm(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function fy(e,t,n){switch(t=is(n),e){case"html":if(e=t.documentElement,!e)throw Error(j(452));return e;case"head":if(e=t.head,!e)throw Error(j(453));return e;case"body":if(e=t.body,!e)throw Error(j(454));return e;default:throw Error(j(451))}}function ti(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Nf(e)}var Dt=new Map,cm=new Set;function os(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Ln=te.d;te.d={f:CS,r:jS,D:RS,C:OS,L:MS,m:DS,X:zS,S:_S,M:US};function CS(){var e=Ln.f(),t=Bs();return e||t}function jS(e){var t=pr(e);t!==null&&t.tag===5&&t.type==="form"?lg(t):Ln.r(e)}var yr=typeof document>"u"?null:document;function dy(e,t,n){var a=yr;if(a&&typeof t=="string"&&t){var l=jt(t);l='link[rel="'+e+'"][href="'+l+'"]',typeof n=="string"&&(l+='[crossorigin="'+n+'"]'),cm.has(l)||(cm.add(l),e={rel:e,crossOrigin:n,href:t},a.querySelector(l)===null&&(t=a.createElement("link"),Pe(t,"link",e),Ge(t),a.head.appendChild(t)))}}function RS(e){Ln.D(e),dy("dns-prefetch",e,null)}function OS(e,t){Ln.C(e,t),dy("preconnect",e,t)}function MS(e,t,n){Ln.L(e,t,n);var a=yr;if(a&&e&&t){var l='link[rel="preload"][as="'+jt(t)+'"]';t==="image"&&n&&n.imageSrcSet?(l+='[imagesrcset="'+jt(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(l+='[imagesizes="'+jt(n.imageSizes)+'"]')):l+='[href="'+jt(e)+'"]';var r=l;switch(t){case"style":r=cr(e);break;case"script":r=br(e)}Dt.has(r)||(e=me({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),Dt.set(r,e),a.querySelector(l)!==null||t==="style"&&a.querySelector(qi(r))||t==="script"&&a.querySelector(Gi(r))||(t=a.createElement("link"),Pe(t,"link",e),Ge(t),a.head.appendChild(t)))}}function DS(e,t){Ln.m(e,t);var n=yr;if(n&&e){var a=t&&typeof t.as=="string"?t.as:"script",l='link[rel="modulepreload"][as="'+jt(a)+'"][href="'+jt(e)+'"]',r=l;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":r=br(e)}if(!Dt.has(r)&&(e=me({rel:"modulepreload",href:e},t),Dt.set(r,e),n.querySelector(l)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Gi(r)))return}a=n.createElement("link"),Pe(a,"link",e),Ge(a),n.head.appendChild(a)}}}function _S(e,t,n){Ln.S(e,t,n);var a=yr;if(a&&e){var l=_l(a).hoistableStyles,r=cr(e);t=t||"default";var i=l.get(r);if(!i){var o={loading:0,preload:null};if(i=a.querySelector(qi(r)))o.loading=5;else{e=me({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Dt.get(r))&&cd(e,n);var s=i=a.createElement("link");Ge(s),Pe(s,"link",e),s._p=new Promise(function(u,d){s.onload=u,s.onerror=d}),s.addEventListener("load",function(){o.loading|=1}),s.addEventListener("error",function(){o.loading|=2}),o.loading|=4,jo(i,t,a)}i={type:"stylesheet",instance:i,count:1,state:o},l.set(r,i)}}}function zS(e,t){Ln.X(e,t);var n=yr;if(n&&e){var a=_l(n).hoistableScripts,l=br(e),r=a.get(l);r||(r=n.querySelector(Gi(l)),r||(e=me({src:e,async:!0},t),(t=Dt.get(l))&&ud(e,t),r=n.createElement("script"),Ge(r),Pe(r,"link",e),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},a.set(l,r))}}function US(e,t){Ln.M(e,t);var n=yr;if(n&&e){var a=_l(n).hoistableScripts,l=br(e),r=a.get(l);r||(r=n.querySelector(Gi(l)),r||(e=me({src:e,async:!0,type:"module"},t),(t=Dt.get(l))&&ud(e,t),r=n.createElement("script"),Ge(r),Pe(r,"link",e),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},a.set(l,r))}}function um(e,t,n,a){var l=(l=ha.current)?os(l):null;if(!l)throw Error(j(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=cr(n.href),n=_l(l).hoistableStyles,a=n.get(t),a||(a={type:"style",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=cr(n.href);var r=_l(l).hoistableStyles,i=r.get(e);if(i||(l=l.ownerDocument||l,i={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},r.set(e,i),(r=l.querySelector(qi(e)))&&!r._p&&(i.instance=r,i.state.loading=5),Dt.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Dt.set(e,n),r||LS(l,e,n,i.state))),t&&a===null)throw Error(j(528,""));return i}if(t&&a!==null)throw Error(j(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=br(n),n=_l(l).hoistableScripts,a=n.get(t),a||(a={type:"script",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(j(444,e))}}function cr(e){return'href="'+jt(e)+'"'}function qi(e){return'link[rel="stylesheet"]['+e+"]"}function hy(e){return me({},e,{"data-precedence":e.precedence,precedence:null})}function LS(e,t,n,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),Pe(t,"link",n),Ge(t),e.head.appendChild(t))}function br(e){return'[src="'+jt(e)+'"]'}function Gi(e){return"script[async]"+e}function fm(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+jt(n.href)+'"]');if(a)return t.instance=a,Ge(a),a;var l=me({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),Ge(a),Pe(a,"style",l),jo(a,n.precedence,e),t.instance=a;case"stylesheet":l=cr(n.href);var r=e.querySelector(qi(l));if(r)return t.state.loading|=4,t.instance=r,Ge(r),r;a=hy(n),(l=Dt.get(l))&&cd(a,l),r=(e.ownerDocument||e).createElement("link"),Ge(r);var i=r;return i._p=new Promise(function(o,s){i.onload=o,i.onerror=s}),Pe(r,"link",a),t.state.loading|=4,jo(r,n.precedence,e),t.instance=r;case"script":return r=br(n.src),(l=e.querySelector(Gi(r)))?(t.instance=l,Ge(l),l):(a=n,(l=Dt.get(r))&&(a=me({},n),ud(a,l)),e=e.ownerDocument||e,l=e.createElement("script"),Ge(l),Pe(l,"link",a),e.head.appendChild(l),t.instance=l);case"void":return null;default:throw Error(j(443,t.type))}else t.type==="stylesheet"&&!(t.state.loading&4)&&(a=t.instance,t.state.loading|=4,jo(a,n.precedence,e));return t.instance}function jo(e,t,n){for(var a=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),l=a.length?a[a.length-1]:null,r=l,i=0;i<a.length;i++){var o=a[i];if(o.dataset.precedence===t)r=o;else if(r!==l)break}r?r.parentNode.insertBefore(e,r.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function cd(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function ud(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Ro=null;function dm(e,t,n){if(Ro===null){var a=new Map,l=Ro=new Map;l.set(n,a)}else l=Ro,a=l.get(n),a||(a=new Map,l.set(n,a));if(a.has(e))return a;for(a.set(e,null),n=n.getElementsByTagName(e),l=0;l<n.length;l++){var r=n[l];if(!(r[Oi]||r[Fe]||e==="link"&&r.getAttribute("rel")==="stylesheet")&&r.namespaceURI!=="http://www.w3.org/2000/svg"){var i=r.getAttribute(t)||"";i=e+i;var o=a.get(i);o?o.push(r):a.set(i,[r])}}return a}function hm(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function BS(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function my(e){return!(e.type==="stylesheet"&&!(e.state.loading&3))}var di=null;function HS(){}function kS(e,t,n){if(di===null)throw Error(j(475));var a=di;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&!(t.state.loading&4)){if(t.instance===null){var l=cr(n.href),r=e.querySelector(qi(l));if(r){e=r._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=ss.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=r,Ge(r);return}r=e.ownerDocument||e,n=hy(n),(l=Dt.get(l))&&cd(n,l),r=r.createElement("link"),Ge(r);var i=r;i._p=new Promise(function(o,s){i.onload=o,i.onerror=s}),Pe(r,"link",n),t.instance=r}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&!(t.state.loading&3)&&(a.count++,t=ss.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function qS(){if(di===null)throw Error(j(475));var e=di;return e.stylesheets&&e.count===0&&Ju(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Ju(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function ss(){if(this.count--,this.count===0){if(this.stylesheets)Ju(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var cs=null;function Ju(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,cs=new Map,t.forEach(GS,e),cs=null,ss.call(e))}function GS(e,t){if(!(t.state.loading&4)){var n=cs.get(e);if(n)var a=n.get(null);else{n=new Map,cs.set(e,n);for(var l=e.querySelectorAll("link[data-precedence],style[data-precedence]"),r=0;r<l.length;r++){var i=l[r];(i.nodeName==="LINK"||i.getAttribute("media")!=="not all")&&(n.set(i.dataset.precedence,i),a=i)}a&&n.set(null,a)}l=t.instance,i=l.getAttribute("data-precedence"),r=n.get(i)||a,r===a&&n.set(null,l),n.set(i,l),this.count++,a=ss.bind(this),l.addEventListener("load",a),l.addEventListener("error",a),r?r.parentNode.insertBefore(l,r.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(l,e.firstChild)),t.state.loading|=4}}var hi={$$typeof:wn,Provider:null,Consumer:null,_currentValue:Ya,_currentValue2:Ya,_threadCount:0};function YS(e,t,n,a,l,r,i,o){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=fc(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=fc(0),this.hiddenUpdates=fc(null),this.identifierPrefix=a,this.onUncaughtError=l,this.onCaughtError=r,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=o,this.incompleteTransitions=new Map}function py(e,t,n,a,l,r,i,o,s,u,d,m){return e=new YS(e,t,n,i,o,s,u,m),t=1,r===!0&&(t|=24),r=mt(3,null,null,t),e.current=r,r.stateNode=e,t=Hf(),t.refCount++,e.pooledCache=t,t.refCount++,r.memoizedState={element:a,isDehydrated:n,cache:t},qf(r),e}function vy(e){return e?(e=Rl,e):Rl}function gy(e,t,n,a,l,r){l=vy(l),a.context===null?a.context=l:a.pendingContext=l,a=ma(t),a.payload={element:n},r=r===void 0?null:r,r!==null&&(a.callback=r),n=pa(e,a,t),n!==null&&(yt(n,e,t),Zr(n,e,t))}function mm(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function fd(e,t){mm(e,t),(e=e.alternate)&&mm(e,t)}function yy(e){if(e.tag===13){var t=vr(e,67108864);t!==null&&yt(t,e,67108864),fd(e,67108864)}}var us=!0;function VS(e,t,n,a){var l=H.T;H.T=null;var r=te.p;try{te.p=2,dd(e,t,n,a)}finally{te.p=r,H.T=l}}function QS(e,t,n,a){var l=H.T;H.T=null;var r=te.p;try{te.p=8,dd(e,t,n,a)}finally{te.p=r,H.T=l}}function dd(e,t,n,a){if(us){var l=$u(a);if(l===null)Yc(e,t,a,fs,n),pm(e,a);else if(PS(l,e,t,n,a))a.stopPropagation();else if(pm(e,a),t&4&&-1<XS.indexOf(e)){for(;l!==null;){var r=pr(l);if(r!==null)switch(r.tag){case 3:if(r=r.stateNode,r.current.memoizedState.isDehydrated){var i=Da(r.pendingLanes);if(i!==0){var o=r;for(o.pendingLanes|=2,o.entangledLanes|=2;i;){var s=1<<31-vt(i);o.entanglements[1]|=s,i&=~s}cn(r),!(re&6)&&(ts=an()+500,ki(0))}}break;case 13:o=vr(r,2),o!==null&&yt(o,r,2),Bs(),fd(r,2)}if(r=$u(a),r===null&&Yc(e,t,a,fs,n),r===l)break;l=r}l!==null&&a.stopPropagation()}else Yc(e,t,a,null,n)}}function $u(e){return e=jf(e),hd(e)}var fs=null;function hd(e){if(fs=null,e=El(e),e!==null){var t=Ni(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=Vp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return fs=e,null}function by(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Ox()){case Zp:return 2;case Kp:return 8;case ko:case Mx:return 32;case Fp:return 268435456;default:return 32}default:return 32}}var Wu=!1,ya=null,ba=null,xa=null,mi=new Map,pi=new Map,na=[],XS="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function pm(e,t){switch(e){case"focusin":case"focusout":ya=null;break;case"dragenter":case"dragleave":ba=null;break;case"mouseover":case"mouseout":xa=null;break;case"pointerover":case"pointerout":mi.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":pi.delete(t.pointerId)}}function Dr(e,t,n,a,l,r){return e===null||e.nativeEvent!==r?(e={blockedOn:t,domEventName:n,eventSystemFlags:a,nativeEvent:r,targetContainers:[l]},t!==null&&(t=pr(t),t!==null&&yy(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function PS(e,t,n,a,l){switch(t){case"focusin":return ya=Dr(ya,e,t,n,a,l),!0;case"dragenter":return ba=Dr(ba,e,t,n,a,l),!0;case"mouseover":return xa=Dr(xa,e,t,n,a,l),!0;case"pointerover":var r=l.pointerId;return mi.set(r,Dr(mi.get(r)||null,e,t,n,a,l)),!0;case"gotpointercapture":return r=l.pointerId,pi.set(r,Dr(pi.get(r)||null,e,t,n,a,l)),!0}return!1}function xy(e){var t=El(e.target);if(t!==null){var n=Ni(t);if(n!==null){if(t=n.tag,t===13){if(t=Vp(n),t!==null){e.blockedOn=t,kx(e.priority,function(){if(n.tag===13){var a=gt();a=Af(a);var l=vr(n,a);l!==null&&yt(l,n,a),fd(n,a)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Oo(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=$u(e.nativeEvent);if(n===null){n=e.nativeEvent;var a=new n.constructor(n.type,n);pu=a,n.target.dispatchEvent(a),pu=null}else return t=pr(n),t!==null&&yy(t),e.blockedOn=n,!1;t.shift()}return!0}function vm(e,t,n){Oo(e)&&n.delete(t)}function ZS(){Wu=!1,ya!==null&&Oo(ya)&&(ya=null),ba!==null&&Oo(ba)&&(ba=null),xa!==null&&Oo(xa)&&(xa=null),mi.forEach(vm),pi.forEach(vm)}function io(e,t){e.blockedOn===t&&(e.blockedOn=null,Wu||(Wu=!0,ke.unstable_scheduleCallback(ke.unstable_NormalPriority,ZS)))}var oo=null;function gm(e){oo!==e&&(oo=e,ke.unstable_scheduleCallback(ke.unstable_NormalPriority,function(){oo===e&&(oo=null);for(var t=0;t<e.length;t+=3){var n=e[t],a=e[t+1],l=e[t+2];if(typeof a!="function"){if(hd(a||n)===null)continue;break}var r=pr(n);r!==null&&(e.splice(t,3),t-=3,Mu(r,{pending:!0,data:l,method:n.method,action:a},a,l))}}))}function vi(e){function t(s){return io(s,e)}ya!==null&&io(ya,e),ba!==null&&io(ba,e),xa!==null&&io(xa,e),mi.forEach(t),pi.forEach(t);for(var n=0;n<na.length;n++){var a=na[n];a.blockedOn===e&&(a.blockedOn=null)}for(;0<na.length&&(n=na[0],n.blockedOn===null);)xy(n),n.blockedOn===null&&na.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(a=0;a<n.length;a+=3){var l=n[a],r=n[a+1],i=l[ot]||null;if(typeof r=="function")i||gm(n);else if(i){var o=null;if(r&&r.hasAttribute("formAction")){if(l=r,i=r[ot]||null)o=i.formAction;else if(hd(l)!==null)continue}else o=i.action;typeof o=="function"?n[a+1]=o:(n.splice(a,3),a-=3),gm(n)}}}function md(e){this._internalRoot=e}Gs.prototype.render=md.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(j(409));var n=t.current,a=gt();gy(n,a,e,t,null,null)};Gs.prototype.unmount=md.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;gy(e.current,2,null,e,null,null),Bs(),t[mr]=null}};function Gs(e){this._internalRoot=e}Gs.prototype.unstable_scheduleHydration=function(e){if(e){var t=ev();e={blockedOn:null,target:e,priority:t};for(var n=0;n<na.length&&t!==0&&t<na[n].priority;n++);na.splice(n,0,e),n===0&&xy(e)}};var ym=Gp.version;if(ym!=="19.1.1")throw Error(j(527,ym,"19.1.1"));te.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(j(188)):(e=Object.keys(e).join(","),Error(j(268,e)));return e=Ex(t),e=e!==null?Qp(e):null,e=e===null?null:e.stateNode,e};var KS={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:H,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var so=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!so.isDisabled&&so.supportsFiber)try{Ci=so.inject(KS),pt=so}catch{}}As.createRoot=function(e,t){if(!Yp(e))throw Error(j(299));var n=!1,a="",l=pg,r=vg,i=gg,o=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(l=t.onUncaughtError),t.onCaughtError!==void 0&&(r=t.onCaughtError),t.onRecoverableError!==void 0&&(i=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(o=t.unstable_transitionCallbacks)),t=py(e,1,!1,null,null,n,a,l,r,i,o,null),e[mr]=t.current,sd(e),new md(t)};As.hydrateRoot=function(e,t,n){if(!Yp(e))throw Error(j(299));var a=!1,l="",r=pg,i=vg,o=gg,s=null,u=null;return n!=null&&(n.unstable_strictMode===!0&&(a=!0),n.identifierPrefix!==void 0&&(l=n.identifierPrefix),n.onUncaughtError!==void 0&&(r=n.onUncaughtError),n.onCaughtError!==void 0&&(i=n.onCaughtError),n.onRecoverableError!==void 0&&(o=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(s=n.unstable_transitionCallbacks),n.formState!==void 0&&(u=n.formState)),t=py(e,1,!0,t,n??null,a,l,r,i,o,s,u),t.context=vy(null),n=t.current,a=gt(),a=Af(a),l=ma(a),l.callback=null,pa(n,l,a),n=a,t.current.lanes=n,Ri(t,n),cn(t),e[mr]=t.current,sd(e),new Gs(t)};As.version="19.1.1";function Sy(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Sy)}catch(e){console.error(e)}}Sy(),Cp.exports=As;var FS=Cp.exports,JS=(e,t,n,a,l,r,i,o)=>{let s=document.documentElement,u=["light","dark"];function d(f){(Array.isArray(e)?e:[e]).forEach(w=>{let y=w==="class",x=y&&r?l.map(v=>r[v]||v):l;y?(s.classList.remove(...x),s.classList.add(r&&r[f]?r[f]:f)):s.setAttribute(w,f)}),m(f)}function m(f){o&&u.includes(f)&&(s.style.colorScheme=f)}function h(){return window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"}if(a)d(a);else try{let f=localStorage.getItem(t)||n,w=i&&f==="system"?h():f;d(w)}catch{}},$S=p.createContext(void 0),WS={setTheme:e=>{},themes:[]},IS=()=>{var e;return(e=p.useContext($S))!=null?e:WS};p.memo(({forcedTheme:e,storageKey:t,attribute:n,enableSystem:a,enableColorScheme:l,defaultTheme:r,value:i,themes:o,nonce:s,scriptProps:u})=>{let d=JSON.stringify([n,t,r,e,o,i,a,l]).slice(1,-1);return p.createElement("script",{...u,suppressHydrationWarning:!0,nonce:typeof window>"u"?s:"",dangerouslySetInnerHTML:{__html:`(${JS.toString()})(${d})`}})});var ew=e=>{switch(e){case"success":return aw;case"info":return rw;case"warning":return lw;case"error":return iw;default:return null}},tw=Array(12).fill(0),nw=({visible:e,className:t})=>D.createElement("div",{className:["sonner-loading-wrapper",t].filter(Boolean).join(" "),"data-visible":e},D.createElement("div",{className:"sonner-spinner"},tw.map((n,a)=>D.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${a}`})))),aw=D.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},D.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),lw=D.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},D.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),rw=D.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},D.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),iw=D.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},D.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),ow=D.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},D.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),D.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),sw=()=>{let[e,t]=D.useState(document.hidden);return D.useEffect(()=>{let n=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",n),()=>window.removeEventListener("visibilitychange",n)},[]),e},Iu=1,cw=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:n,...a}=e,l=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:Iu++,r=this.toasts.find(o=>o.id===l),i=e.dismissible===void 0?!0:e.dismissible;return this.dismissedToasts.has(l)&&this.dismissedToasts.delete(l),r?this.toasts=this.toasts.map(o=>o.id===l?(this.publish({...o,...e,id:l,title:n}),{...o,...e,id:l,dismissible:i,title:n}):o):this.addToast({title:n,...a,dismissible:i,id:l}),l},this.dismiss=e=>(this.dismissedToasts.add(e),e||this.toasts.forEach(t=>{this.subscribers.forEach(n=>n({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let n;t.loading!==void 0&&(n=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let a=e instanceof Promise?e:e(),l=n!==void 0,r,i=a.then(async s=>{if(r=["resolve",s],D.isValidElement(s))l=!1,this.create({id:n,type:"default",message:s});else if(fw(s)&&!s.ok){l=!1;let u=typeof t.error=="function"?await t.error(`HTTP error! status: ${s.status}`):t.error,d=typeof t.description=="function"?await t.description(`HTTP error! status: ${s.status}`):t.description;this.create({id:n,type:"error",message:u,description:d})}else if(t.success!==void 0){l=!1;let u=typeof t.success=="function"?await t.success(s):t.success,d=typeof t.description=="function"?await t.description(s):t.description;this.create({id:n,type:"success",message:u,description:d})}}).catch(async s=>{if(r=["reject",s],t.error!==void 0){l=!1;let u=typeof t.error=="function"?await t.error(s):t.error,d=typeof t.description=="function"?await t.description(s):t.description;this.create({id:n,type:"error",message:u,description:d})}}).finally(()=>{var s;l&&(this.dismiss(n),n=void 0),(s=t.finally)==null||s.call(t)}),o=()=>new Promise((s,u)=>i.then(()=>r[0]==="reject"?u(r[1]):s(r[1])).catch(u));return typeof n!="string"&&typeof n!="number"?{unwrap:o}:Object.assign(n,{unwrap:o})},this.custom=(e,t)=>{let n=(t==null?void 0:t.id)||Iu++;return this.create({jsx:e(n),id:n,...t}),n},this.getActiveToasts=()=>this.toasts.filter(e=>!this.dismissedToasts.has(e.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},rt=new cw,uw=(e,t)=>{let n=(t==null?void 0:t.id)||Iu++;return rt.addToast({title:e,...t,id:n}),n},fw=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",dw=uw,hw=()=>rt.toasts,mw=()=>rt.getActiveToasts();Object.assign(dw,{success:rt.success,info:rt.info,warning:rt.warning,error:rt.error,custom:rt.custom,message:rt.message,promise:rt.promise,dismiss:rt.dismiss,loading:rt.loading},{getHistory:hw,getToasts:mw});function pw(e,{insertAt:t}={}){if(typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],a=document.createElement("style");a.type="text/css",t==="top"&&n.firstChild?n.insertBefore(a,n.firstChild):n.appendChild(a),a.styleSheet?a.styleSheet.cssText=e:a.appendChild(document.createTextNode(e))}pw(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position="left"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function co(e){return e.label!==void 0}var vw=3,gw="32px",yw="16px",bm=4e3,bw=356,xw=14,Sw=20,ww=200;function Bt(...e){return e.filter(Boolean).join(" ")}function Ew(e){let[t,n]=e.split("-"),a=[];return t&&a.push(t),n&&a.push(n),a}var Aw=e=>{var t,n,a,l,r,i,o,s,u,d,m;let{invert:h,toast:f,unstyled:w,interacting:y,setHeights:x,visibleToasts:v,heights:g,index:b,toasts:S,expanded:E,removeToast:T,defaultRichColors:A,closeButton:C,style:O,cancelButtonStyle:M,actionButtonStyle:B,className:L="",descriptionClassName:$="",duration:Z,position:ce,gap:R,loadingIcon:U,expandByDefault:_,classNames:z,icons:k,closeButtonAriaLabel:Ee="Close toast",pauseWhenPageIsHidden:K}=e,[I,J]=D.useState(null),[Re,Bn]=D.useState(null),[de,il]=D.useState(!1),[Hn,ja]=D.useState(!1),[kn,ol]=D.useState(!1),[qn,Qi]=D.useState(!1),[lc,Xi]=D.useState(!1),[rc,Er]=D.useState(0),[sl,Gd]=D.useState(0),Ar=D.useRef(f.duration||Z||bm),Yd=D.useRef(null),Ra=D.useRef(null),Xb=b===0,Pb=b+1<=v,St=f.type,cl=f.dismissible!==!1,Zb=f.className||"",Kb=f.descriptionClassName||"",Pi=D.useMemo(()=>g.findIndex(q=>q.toastId===f.id)||0,[g,f.id]),Fb=D.useMemo(()=>{var q;return(q=f.closeButton)!=null?q:C},[f.closeButton,C]),Vd=D.useMemo(()=>f.duration||Z||bm,[f.duration,Z]),ic=D.useRef(0),ul=D.useRef(0),Qd=D.useRef(0),fl=D.useRef(null),[Jb,$b]=ce.split("-"),Xd=D.useMemo(()=>g.reduce((q,ie,ve)=>ve>=Pi?q:q+ie.height,0),[g,Pi]),Pd=sw(),Wb=f.invert||h,oc=St==="loading";ul.current=D.useMemo(()=>Pi*R+Xd,[Pi,Xd]),D.useEffect(()=>{Ar.current=Vd},[Vd]),D.useEffect(()=>{il(!0)},[]),D.useEffect(()=>{let q=Ra.current;if(q){let ie=q.getBoundingClientRect().height;return Gd(ie),x(ve=>[{toastId:f.id,height:ie,position:f.position},...ve]),()=>x(ve=>ve.filter(zt=>zt.toastId!==f.id))}},[x,f.id]),D.useLayoutEffect(()=>{if(!de)return;let q=Ra.current,ie=q.style.height;q.style.height="auto";let ve=q.getBoundingClientRect().height;q.style.height=ie,Gd(ve),x(zt=>zt.find(Ut=>Ut.toastId===f.id)?zt.map(Ut=>Ut.toastId===f.id?{...Ut,height:ve}:Ut):[{toastId:f.id,height:ve,position:f.position},...zt])},[de,f.title,f.description,x,f.id]);let Gn=D.useCallback(()=>{ja(!0),Er(ul.current),x(q=>q.filter(ie=>ie.toastId!==f.id)),setTimeout(()=>{T(f)},ww)},[f,T,x,ul]);D.useEffect(()=>{if(f.promise&&St==="loading"||f.duration===1/0||f.type==="loading")return;let q;return E||y||K&&Pd?(()=>{if(Qd.current<ic.current){let ie=new Date().getTime()-ic.current;Ar.current=Ar.current-ie}Qd.current=new Date().getTime()})():Ar.current!==1/0&&(ic.current=new Date().getTime(),q=setTimeout(()=>{var ie;(ie=f.onAutoClose)==null||ie.call(f,f),Gn()},Ar.current)),()=>clearTimeout(q)},[E,y,f,St,K,Pd,Gn]),D.useEffect(()=>{f.delete&&Gn()},[Gn,f.delete]);function Ib(){var q,ie,ve;return k!=null&&k.loading?D.createElement("div",{className:Bt(z==null?void 0:z.loader,(q=f==null?void 0:f.classNames)==null?void 0:q.loader,"sonner-loader"),"data-visible":St==="loading"},k.loading):U?D.createElement("div",{className:Bt(z==null?void 0:z.loader,(ie=f==null?void 0:f.classNames)==null?void 0:ie.loader,"sonner-loader"),"data-visible":St==="loading"},U):D.createElement(nw,{className:Bt(z==null?void 0:z.loader,(ve=f==null?void 0:f.classNames)==null?void 0:ve.loader),visible:St==="loading"})}return D.createElement("li",{tabIndex:0,ref:Ra,className:Bt(L,Zb,z==null?void 0:z.toast,(t=f==null?void 0:f.classNames)==null?void 0:t.toast,z==null?void 0:z.default,z==null?void 0:z[St],(n=f==null?void 0:f.classNames)==null?void 0:n[St]),"data-sonner-toast":"","data-rich-colors":(a=f.richColors)!=null?a:A,"data-styled":!(f.jsx||f.unstyled||w),"data-mounted":de,"data-promise":!!f.promise,"data-swiped":lc,"data-removed":Hn,"data-visible":Pb,"data-y-position":Jb,"data-x-position":$b,"data-index":b,"data-front":Xb,"data-swiping":kn,"data-dismissible":cl,"data-type":St,"data-invert":Wb,"data-swipe-out":qn,"data-swipe-direction":Re,"data-expanded":!!(E||_&&de),style:{"--index":b,"--toasts-before":b,"--z-index":S.length-b,"--offset":`${Hn?rc:ul.current}px`,"--initial-height":_?"auto":`${sl}px`,...O,...f.style},onDragEnd:()=>{ol(!1),J(null),fl.current=null},onPointerDown:q=>{oc||!cl||(Yd.current=new Date,Er(ul.current),q.target.setPointerCapture(q.pointerId),q.target.tagName!=="BUTTON"&&(ol(!0),fl.current={x:q.clientX,y:q.clientY}))},onPointerUp:()=>{var q,ie,ve,zt;if(qn||!cl)return;fl.current=null;let Ut=Number(((q=Ra.current)==null?void 0:q.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),Yn=Number(((ie=Ra.current)==null?void 0:ie.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),Oa=new Date().getTime()-((ve=Yd.current)==null?void 0:ve.getTime()),Lt=I==="x"?Ut:Yn,Vn=Math.abs(Lt)/Oa;if(Math.abs(Lt)>=Sw||Vn>.11){Er(ul.current),(zt=f.onDismiss)==null||zt.call(f,f),Bn(I==="x"?Ut>0?"right":"left":Yn>0?"down":"up"),Gn(),Qi(!0),Xi(!1);return}ol(!1),J(null)},onPointerMove:q=>{var ie,ve,zt,Ut;if(!fl.current||!cl||((ie=window.getSelection())==null?void 0:ie.toString().length)>0)return;let Yn=q.clientY-fl.current.y,Oa=q.clientX-fl.current.x,Lt=(ve=e.swipeDirections)!=null?ve:Ew(ce);!I&&(Math.abs(Oa)>1||Math.abs(Yn)>1)&&J(Math.abs(Oa)>Math.abs(Yn)?"x":"y");let Vn={x:0,y:0};I==="y"?(Lt.includes("top")||Lt.includes("bottom"))&&(Lt.includes("top")&&Yn<0||Lt.includes("bottom")&&Yn>0)&&(Vn.y=Yn):I==="x"&&(Lt.includes("left")||Lt.includes("right"))&&(Lt.includes("left")&&Oa<0||Lt.includes("right")&&Oa>0)&&(Vn.x=Oa),(Math.abs(Vn.x)>0||Math.abs(Vn.y)>0)&&Xi(!0),(zt=Ra.current)==null||zt.style.setProperty("--swipe-amount-x",`${Vn.x}px`),(Ut=Ra.current)==null||Ut.style.setProperty("--swipe-amount-y",`${Vn.y}px`)}},Fb&&!f.jsx?D.createElement("button",{"aria-label":Ee,"data-disabled":oc,"data-close-button":!0,onClick:oc||!cl?()=>{}:()=>{var q;Gn(),(q=f.onDismiss)==null||q.call(f,f)},className:Bt(z==null?void 0:z.closeButton,(l=f==null?void 0:f.classNames)==null?void 0:l.closeButton)},(r=k==null?void 0:k.close)!=null?r:ow):null,f.jsx||p.isValidElement(f.title)?f.jsx?f.jsx:typeof f.title=="function"?f.title():f.title:D.createElement(D.Fragment,null,St||f.icon||f.promise?D.createElement("div",{"data-icon":"",className:Bt(z==null?void 0:z.icon,(i=f==null?void 0:f.classNames)==null?void 0:i.icon)},f.promise||f.type==="loading"&&!f.icon?f.icon||Ib():null,f.type!=="loading"?f.icon||(k==null?void 0:k[St])||ew(St):null):null,D.createElement("div",{"data-content":"",className:Bt(z==null?void 0:z.content,(o=f==null?void 0:f.classNames)==null?void 0:o.content)},D.createElement("div",{"data-title":"",className:Bt(z==null?void 0:z.title,(s=f==null?void 0:f.classNames)==null?void 0:s.title)},typeof f.title=="function"?f.title():f.title),f.description?D.createElement("div",{"data-description":"",className:Bt($,Kb,z==null?void 0:z.description,(u=f==null?void 0:f.classNames)==null?void 0:u.description)},typeof f.description=="function"?f.description():f.description):null),p.isValidElement(f.cancel)?f.cancel:f.cancel&&co(f.cancel)?D.createElement("button",{"data-button":!0,"data-cancel":!0,style:f.cancelButtonStyle||M,onClick:q=>{var ie,ve;co(f.cancel)&&cl&&((ve=(ie=f.cancel).onClick)==null||ve.call(ie,q),Gn())},className:Bt(z==null?void 0:z.cancelButton,(d=f==null?void 0:f.classNames)==null?void 0:d.cancelButton)},f.cancel.label):null,p.isValidElement(f.action)?f.action:f.action&&co(f.action)?D.createElement("button",{"data-button":!0,"data-action":!0,style:f.actionButtonStyle||B,onClick:q=>{var ie,ve;co(f.action)&&((ve=(ie=f.action).onClick)==null||ve.call(ie,q),!q.defaultPrevented&&Gn())},className:Bt(z==null?void 0:z.actionButton,(m=f==null?void 0:f.classNames)==null?void 0:m.actionButton)},f.action.label):null))};function xm(){if(typeof window>"u"||typeof document>"u")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}function Tw(e,t){let n={};return[e,t].forEach((a,l)=>{let r=l===1,i=r?"--mobile-offset":"--offset",o=r?yw:gw;function s(u){["top","right","bottom","left"].forEach(d=>{n[`${i}-${d}`]=typeof u=="number"?`${u}px`:u})}typeof a=="number"||typeof a=="string"?s(a):typeof a=="object"?["top","right","bottom","left"].forEach(u=>{a[u]===void 0?n[`${i}-${u}`]=o:n[`${i}-${u}`]=typeof a[u]=="number"?`${a[u]}px`:a[u]}):s(o)}),n}var Nw=p.forwardRef(function(e,t){let{invert:n,position:a="bottom-right",hotkey:l=["altKey","KeyT"],expand:r,closeButton:i,className:o,offset:s,mobileOffset:u,theme:d="light",richColors:m,duration:h,style:f,visibleToasts:w=vw,toastOptions:y,dir:x=xm(),gap:v=xw,loadingIcon:g,icons:b,containerAriaLabel:S="Notifications",pauseWhenPageIsHidden:E}=e,[T,A]=D.useState([]),C=D.useMemo(()=>Array.from(new Set([a].concat(T.filter(K=>K.position).map(K=>K.position)))),[T,a]),[O,M]=D.useState([]),[B,L]=D.useState(!1),[$,Z]=D.useState(!1),[ce,R]=D.useState(d!=="system"?d:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),U=D.useRef(null),_=l.join("+").replace(/Key/g,"").replace(/Digit/g,""),z=D.useRef(null),k=D.useRef(!1),Ee=D.useCallback(K=>{A(I=>{var J;return(J=I.find(Re=>Re.id===K.id))!=null&&J.delete||rt.dismiss(K.id),I.filter(({id:Re})=>Re!==K.id)})},[]);return D.useEffect(()=>rt.subscribe(K=>{if(K.dismiss){A(I=>I.map(J=>J.id===K.id?{...J,delete:!0}:J));return}setTimeout(()=>{qp.flushSync(()=>{A(I=>{let J=I.findIndex(Re=>Re.id===K.id);return J!==-1?[...I.slice(0,J),{...I[J],...K},...I.slice(J+1)]:[K,...I]})})})}),[]),D.useEffect(()=>{if(d!=="system"){R(d);return}if(d==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?R("dark"):R("light")),typeof window>"u")return;let K=window.matchMedia("(prefers-color-scheme: dark)");try{K.addEventListener("change",({matches:I})=>{R(I?"dark":"light")})}catch{K.addListener(({matches:J})=>{try{R(J?"dark":"light")}catch(Re){console.error(Re)}})}},[d]),D.useEffect(()=>{T.length<=1&&L(!1)},[T]),D.useEffect(()=>{let K=I=>{var J,Re;l.every(Bn=>I[Bn]||I.code===Bn)&&(L(!0),(J=U.current)==null||J.focus()),I.code==="Escape"&&(document.activeElement===U.current||(Re=U.current)!=null&&Re.contains(document.activeElement))&&L(!1)};return document.addEventListener("keydown",K),()=>document.removeEventListener("keydown",K)},[l]),D.useEffect(()=>{if(U.current)return()=>{z.current&&(z.current.focus({preventScroll:!0}),z.current=null,k.current=!1)}},[U.current]),D.createElement("section",{ref:t,"aria-label":`${S} ${_}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},C.map((K,I)=>{var J;let[Re,Bn]=K.split("-");return T.length?D.createElement("ol",{key:K,dir:x==="auto"?xm():x,tabIndex:-1,ref:U,className:o,"data-sonner-toaster":!0,"data-theme":ce,"data-y-position":Re,"data-lifted":B&&T.length>1&&!r,"data-x-position":Bn,style:{"--front-toast-height":`${((J=O[0])==null?void 0:J.height)||0}px`,"--width":`${bw}px`,"--gap":`${v}px`,...f,...Tw(s,u)},onBlur:de=>{k.current&&!de.currentTarget.contains(de.relatedTarget)&&(k.current=!1,z.current&&(z.current.focus({preventScroll:!0}),z.current=null))},onFocus:de=>{de.target instanceof HTMLElement&&de.target.dataset.dismissible==="false"||k.current||(k.current=!0,z.current=de.relatedTarget)},onMouseEnter:()=>L(!0),onMouseMove:()=>L(!0),onMouseLeave:()=>{$||L(!1)},onDragEnd:()=>L(!1),onPointerDown:de=>{de.target instanceof HTMLElement&&de.target.dataset.dismissible==="false"||Z(!0)},onPointerUp:()=>Z(!1)},T.filter(de=>!de.position&&I===0||de.position===K).map((de,il)=>{var Hn,ja;return D.createElement(Aw,{key:de.id,icons:b,index:il,toast:de,defaultRichColors:m,duration:(Hn=y==null?void 0:y.duration)!=null?Hn:h,className:y==null?void 0:y.className,descriptionClassName:y==null?void 0:y.descriptionClassName,invert:n,visibleToasts:w,closeButton:(ja=y==null?void 0:y.closeButton)!=null?ja:i,interacting:$,position:K,style:y==null?void 0:y.style,unstyled:y==null?void 0:y.unstyled,classNames:y==null?void 0:y.classNames,cancelButtonStyle:y==null?void 0:y.cancelButtonStyle,actionButtonStyle:y==null?void 0:y.actionButtonStyle,removeToast:Ee,toasts:T.filter(kn=>kn.position==de.position),heights:O.filter(kn=>kn.position==de.position),setHeights:M,expandByDefault:r,gap:v,loadingIcon:g,expanded:B,pauseWhenPageIsHidden:E,swipeDirections:e.swipeDirections})})):null}))});const Cw=({...e})=>{const{theme:t="system"}=IS();return c.jsx(Nw,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})};function ne(e,t,{checkForDefaultPrevented:n=!0}={}){return function(l){if(e==null||e(l),n===!1||!l.defaultPrevented)return t==null?void 0:t(l)}}function Sm(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function wy(...e){return t=>{let n=!1;const a=e.map(l=>{const r=Sm(l,t);return!n&&typeof r=="function"&&(n=!0),r});if(n)return()=>{for(let l=0;l<a.length;l++){const r=a[l];typeof r=="function"?r():Sm(e[l],null)}}}}function Le(...e){return p.useCallback(wy(...e),e)}function jw(e,t){const n=p.createContext(t),a=r=>{const{children:i,...o}=r,s=p.useMemo(()=>o,Object.values(o));return c.jsx(n.Provider,{value:s,children:i})};a.displayName=e+"Provider";function l(r){const i=p.useContext(n);if(i)return i;if(t!==void 0)return t;throw new Error(`\`${r}\` must be used within \`${e}\``)}return[a,l]}function rl(e,t=[]){let n=[];function a(r,i){const o=p.createContext(i),s=n.length;n=[...n,i];const u=m=>{var v;const{scope:h,children:f,...w}=m,y=((v=h==null?void 0:h[e])==null?void 0:v[s])||o,x=p.useMemo(()=>w,Object.values(w));return c.jsx(y.Provider,{value:x,children:f})};u.displayName=r+"Provider";function d(m,h){var y;const f=((y=h==null?void 0:h[e])==null?void 0:y[s])||o,w=p.useContext(f);if(w)return w;if(i!==void 0)return i;throw new Error(`\`${m}\` must be used within \`${r}\``)}return[u,d]}const l=()=>{const r=n.map(i=>p.createContext(i));return function(o){const s=(o==null?void 0:o[e])||r;return p.useMemo(()=>({[`__scope${e}`]:{...o,[e]:s}}),[o,s])}};return l.scopeName=e,[a,Rw(l,...t)]}function Rw(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const a=e.map(l=>({useScope:l(),scopeName:l.scopeName}));return function(r){const i=a.reduce((o,{useScope:s,scopeName:u})=>{const m=s(r)[`__scope${u}`];return{...o,...m}},{});return p.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function gi(e){const t=Mw(e),n=p.forwardRef((a,l)=>{const{children:r,...i}=a,o=p.Children.toArray(r),s=o.find(_w);if(s){const u=s.props.children,d=o.map(m=>m===s?p.Children.count(u)>1?p.Children.only(null):p.isValidElement(u)?u.props.children:null:m);return c.jsx(t,{...i,ref:l,children:p.isValidElement(u)?p.cloneElement(u,void 0,d):null})}return c.jsx(t,{...i,ref:l,children:r})});return n.displayName=`${e}.Slot`,n}var Ow=gi("Slot");function Mw(e){const t=p.forwardRef((n,a)=>{const{children:l,...r}=n;if(p.isValidElement(l)){const i=Uw(l),o=zw(r,l.props);return l.type!==p.Fragment&&(o.ref=a?wy(a,i):i),p.cloneElement(l,o)}return p.Children.count(l)>1?p.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Ey=Symbol("radix.slottable");function Dw(e){const t=({children:n})=>c.jsx(c.Fragment,{children:n});return t.displayName=`${e}.Slottable`,t.__radixId=Ey,t}function _w(e){return p.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Ey}function zw(e,t){const n={...t};for(const a in t){const l=e[a],r=t[a];/^on[A-Z]/.test(a)?l&&r?n[a]=(...o)=>{const s=r(...o);return l(...o),s}:l&&(n[a]=l):a==="style"?n[a]={...l,...r}:a==="className"&&(n[a]=[l,r].filter(Boolean).join(" "))}return{...e,...n}}function Uw(e){var a,l;let t=(a=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:a.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(l=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:l.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Lw=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],pe=Lw.reduce((e,t)=>{const n=gi(`Primitive.${t}`),a=p.forwardRef((l,r)=>{const{asChild:i,...o}=l,s=i?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),c.jsx(s,{...o,ref:r})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function Bw(e,t){e&&Ns.flushSync(()=>e.dispatchEvent(t))}function it(e){const t=p.useRef(e);return p.useEffect(()=>{t.current=e}),p.useMemo(()=>(...n)=>{var a;return(a=t.current)==null?void 0:a.call(t,...n)},[])}function Hw(e,t=globalThis==null?void 0:globalThis.document){const n=it(e);p.useEffect(()=>{const a=l=>{l.key==="Escape"&&n(l)};return t.addEventListener("keydown",a,{capture:!0}),()=>t.removeEventListener("keydown",a,{capture:!0})},[n,t])}var kw="DismissableLayer",ef="dismissableLayer.update",qw="dismissableLayer.pointerDownOutside",Gw="dismissableLayer.focusOutside",wm,Ay=p.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),pd=p.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:a,onPointerDownOutside:l,onFocusOutside:r,onInteractOutside:i,onDismiss:o,...s}=e,u=p.useContext(Ay),[d,m]=p.useState(null),h=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,f]=p.useState({}),w=Le(t,A=>m(A)),y=Array.from(u.layers),[x]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),v=y.indexOf(x),g=d?y.indexOf(d):-1,b=u.layersWithOutsidePointerEventsDisabled.size>0,S=g>=v,E=Qw(A=>{const C=A.target,O=[...u.branches].some(M=>M.contains(C));!S||O||(l==null||l(A),i==null||i(A),A.defaultPrevented||o==null||o())},h),T=Xw(A=>{const C=A.target;[...u.branches].some(M=>M.contains(C))||(r==null||r(A),i==null||i(A),A.defaultPrevented||o==null||o())},h);return Hw(A=>{g===u.layers.size-1&&(a==null||a(A),!A.defaultPrevented&&o&&(A.preventDefault(),o()))},h),p.useEffect(()=>{if(d)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(wm=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),Em(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(h.body.style.pointerEvents=wm)}},[d,h,n,u]),p.useEffect(()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),Em())},[d,u]),p.useEffect(()=>{const A=()=>f({});return document.addEventListener(ef,A),()=>document.removeEventListener(ef,A)},[]),c.jsx(pe.div,{...s,ref:w,style:{pointerEvents:b?S?"auto":"none":void 0,...e.style},onFocusCapture:ne(e.onFocusCapture,T.onFocusCapture),onBlurCapture:ne(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:ne(e.onPointerDownCapture,E.onPointerDownCapture)})});pd.displayName=kw;var Yw="DismissableLayerBranch",Vw=p.forwardRef((e,t)=>{const n=p.useContext(Ay),a=p.useRef(null),l=Le(t,a);return p.useEffect(()=>{const r=a.current;if(r)return n.branches.add(r),()=>{n.branches.delete(r)}},[n.branches]),c.jsx(pe.div,{...e,ref:l})});Vw.displayName=Yw;function Qw(e,t=globalThis==null?void 0:globalThis.document){const n=it(e),a=p.useRef(!1),l=p.useRef(()=>{});return p.useEffect(()=>{const r=o=>{if(o.target&&!a.current){let s=function(){Ty(qw,n,u,{discrete:!0})};const u={originalEvent:o};o.pointerType==="touch"?(t.removeEventListener("click",l.current),l.current=s,t.addEventListener("click",l.current,{once:!0})):s()}else t.removeEventListener("click",l.current);a.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",r)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",r),t.removeEventListener("click",l.current)}},[t,n]),{onPointerDownCapture:()=>a.current=!0}}function Xw(e,t=globalThis==null?void 0:globalThis.document){const n=it(e),a=p.useRef(!1);return p.useEffect(()=>{const l=r=>{r.target&&!a.current&&Ty(Gw,n,{originalEvent:r},{discrete:!1})};return t.addEventListener("focusin",l),()=>t.removeEventListener("focusin",l)},[t,n]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}function Em(){const e=new CustomEvent(ef);document.dispatchEvent(e)}function Ty(e,t,n,{discrete:a}){const l=n.originalEvent.target,r=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&l.addEventListener(e,t,{once:!0}),a?Bw(l,r):l.dispatchEvent(r)}var _n=globalThis!=null&&globalThis.document?p.useLayoutEffect:()=>{},Pw=xf[" useId ".trim().toString()]||(()=>{}),Zw=0;function ni(e){const[t,n]=p.useState(Pw());return _n(()=>{n(a=>a??String(Zw++))},[e]),t?`radix-${t}`:""}const Kw=["top","right","bottom","left"],Aa=Math.min,ht=Math.max,ds=Math.round,uo=Math.floor,rn=e=>({x:e,y:e}),Fw={left:"right",right:"left",bottom:"top",top:"bottom"},Jw={start:"end",end:"start"};function tf(e,t,n){return ht(e,Aa(t,n))}function zn(e,t){return typeof e=="function"?e(t):e}function Un(e){return e.split("-")[0]}function xr(e){return e.split("-")[1]}function vd(e){return e==="x"?"y":"x"}function gd(e){return e==="y"?"height":"width"}const $w=new Set(["top","bottom"]);function tn(e){return $w.has(Un(e))?"y":"x"}function yd(e){return vd(tn(e))}function Ww(e,t,n){n===void 0&&(n=!1);const a=xr(e),l=yd(e),r=gd(l);let i=l==="x"?a===(n?"end":"start")?"right":"left":a==="start"?"bottom":"top";return t.reference[r]>t.floating[r]&&(i=hs(i)),[i,hs(i)]}function Iw(e){const t=hs(e);return[nf(e),t,nf(t)]}function nf(e){return e.replace(/start|end/g,t=>Jw[t])}const Am=["left","right"],Tm=["right","left"],e2=["top","bottom"],t2=["bottom","top"];function n2(e,t,n){switch(e){case"top":case"bottom":return n?t?Tm:Am:t?Am:Tm;case"left":case"right":return t?e2:t2;default:return[]}}function a2(e,t,n,a){const l=xr(e);let r=n2(Un(e),n==="start",a);return l&&(r=r.map(i=>i+"-"+l),t&&(r=r.concat(r.map(nf)))),r}function hs(e){return e.replace(/left|right|bottom|top/g,t=>Fw[t])}function l2(e){return{top:0,right:0,bottom:0,left:0,...e}}function Ny(e){return typeof e!="number"?l2(e):{top:e,right:e,bottom:e,left:e}}function ms(e){const{x:t,y:n,width:a,height:l}=e;return{width:a,height:l,top:n,left:t,right:t+a,bottom:n+l,x:t,y:n}}function Nm(e,t,n){let{reference:a,floating:l}=e;const r=tn(t),i=yd(t),o=gd(i),s=Un(t),u=r==="y",d=a.x+a.width/2-l.width/2,m=a.y+a.height/2-l.height/2,h=a[o]/2-l[o]/2;let f;switch(s){case"top":f={x:d,y:a.y-l.height};break;case"bottom":f={x:d,y:a.y+a.height};break;case"right":f={x:a.x+a.width,y:m};break;case"left":f={x:a.x-l.width,y:m};break;default:f={x:a.x,y:a.y}}switch(xr(t)){case"start":f[i]-=h*(n&&u?-1:1);break;case"end":f[i]+=h*(n&&u?-1:1);break}return f}const r2=async(e,t,n)=>{const{placement:a="bottom",strategy:l="absolute",middleware:r=[],platform:i}=n,o=r.filter(Boolean),s=await(i.isRTL==null?void 0:i.isRTL(t));let u=await i.getElementRects({reference:e,floating:t,strategy:l}),{x:d,y:m}=Nm(u,a,s),h=a,f={},w=0;for(let y=0;y<o.length;y++){const{name:x,fn:v}=o[y],{x:g,y:b,data:S,reset:E}=await v({x:d,y:m,initialPlacement:a,placement:h,strategy:l,middlewareData:f,rects:u,platform:i,elements:{reference:e,floating:t}});d=g??d,m=b??m,f={...f,[x]:{...f[x],...S}},E&&w<=50&&(w++,typeof E=="object"&&(E.placement&&(h=E.placement),E.rects&&(u=E.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:l}):E.rects),{x:d,y:m}=Nm(u,h,s)),y=-1)}return{x:d,y:m,placement:h,strategy:l,middlewareData:f}};async function yi(e,t){var n;t===void 0&&(t={});const{x:a,y:l,platform:r,rects:i,elements:o,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:m="floating",altBoundary:h=!1,padding:f=0}=zn(t,e),w=Ny(f),x=o[h?m==="floating"?"reference":"floating":m],v=ms(await r.getClippingRect({element:(n=await(r.isElement==null?void 0:r.isElement(x)))==null||n?x:x.contextElement||await(r.getDocumentElement==null?void 0:r.getDocumentElement(o.floating)),boundary:u,rootBoundary:d,strategy:s})),g=m==="floating"?{x:a,y:l,width:i.floating.width,height:i.floating.height}:i.reference,b=await(r.getOffsetParent==null?void 0:r.getOffsetParent(o.floating)),S=await(r.isElement==null?void 0:r.isElement(b))?await(r.getScale==null?void 0:r.getScale(b))||{x:1,y:1}:{x:1,y:1},E=ms(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:o,rect:g,offsetParent:b,strategy:s}):g);return{top:(v.top-E.top+w.top)/S.y,bottom:(E.bottom-v.bottom+w.bottom)/S.y,left:(v.left-E.left+w.left)/S.x,right:(E.right-v.right+w.right)/S.x}}const i2=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:a,placement:l,rects:r,platform:i,elements:o,middlewareData:s}=t,{element:u,padding:d=0}=zn(e,t)||{};if(u==null)return{};const m=Ny(d),h={x:n,y:a},f=yd(l),w=gd(f),y=await i.getDimensions(u),x=f==="y",v=x?"top":"left",g=x?"bottom":"right",b=x?"clientHeight":"clientWidth",S=r.reference[w]+r.reference[f]-h[f]-r.floating[w],E=h[f]-r.reference[f],T=await(i.getOffsetParent==null?void 0:i.getOffsetParent(u));let A=T?T[b]:0;(!A||!await(i.isElement==null?void 0:i.isElement(T)))&&(A=o.floating[b]||r.floating[w]);const C=S/2-E/2,O=A/2-y[w]/2-1,M=Aa(m[v],O),B=Aa(m[g],O),L=M,$=A-y[w]-B,Z=A/2-y[w]/2+C,ce=tf(L,Z,$),R=!s.arrow&&xr(l)!=null&&Z!==ce&&r.reference[w]/2-(Z<L?M:B)-y[w]/2<0,U=R?Z<L?Z-L:Z-$:0;return{[f]:h[f]+U,data:{[f]:ce,centerOffset:Z-ce-U,...R&&{alignmentOffset:U}},reset:R}}}),o2=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,a;const{placement:l,middlewareData:r,rects:i,initialPlacement:o,platform:s,elements:u}=t,{mainAxis:d=!0,crossAxis:m=!0,fallbackPlacements:h,fallbackStrategy:f="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:y=!0,...x}=zn(e,t);if((n=r.arrow)!=null&&n.alignmentOffset)return{};const v=Un(l),g=tn(o),b=Un(o)===o,S=await(s.isRTL==null?void 0:s.isRTL(u.floating)),E=h||(b||!y?[hs(o)]:Iw(o)),T=w!=="none";!h&&T&&E.push(...a2(o,y,w,S));const A=[o,...E],C=await yi(t,x),O=[];let M=((a=r.flip)==null?void 0:a.overflows)||[];if(d&&O.push(C[v]),m){const Z=Ww(l,i,S);O.push(C[Z[0]],C[Z[1]])}if(M=[...M,{placement:l,overflows:O}],!O.every(Z=>Z<=0)){var B,L;const Z=(((B=r.flip)==null?void 0:B.index)||0)+1,ce=A[Z];if(ce&&(!(m==="alignment"?g!==tn(ce):!1)||M.every(_=>_.overflows[0]>0&&tn(_.placement)===g)))return{data:{index:Z,overflows:M},reset:{placement:ce}};let R=(L=M.filter(U=>U.overflows[0]<=0).sort((U,_)=>U.overflows[1]-_.overflows[1])[0])==null?void 0:L.placement;if(!R)switch(f){case"bestFit":{var $;const U=($=M.filter(_=>{if(T){const z=tn(_.placement);return z===g||z==="y"}return!0}).map(_=>[_.placement,_.overflows.filter(z=>z>0).reduce((z,k)=>z+k,0)]).sort((_,z)=>_[1]-z[1])[0])==null?void 0:$[0];U&&(R=U);break}case"initialPlacement":R=o;break}if(l!==R)return{reset:{placement:R}}}return{}}}};function Cm(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function jm(e){return Kw.some(t=>e[t]>=0)}const s2=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:a="referenceHidden",...l}=zn(e,t);switch(a){case"referenceHidden":{const r=await yi(t,{...l,elementContext:"reference"}),i=Cm(r,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:jm(i)}}}case"escaped":{const r=await yi(t,{...l,altBoundary:!0}),i=Cm(r,n.floating);return{data:{escapedOffsets:i,escaped:jm(i)}}}default:return{}}}}},Cy=new Set(["left","top"]);async function c2(e,t){const{placement:n,platform:a,elements:l}=e,r=await(a.isRTL==null?void 0:a.isRTL(l.floating)),i=Un(n),o=xr(n),s=tn(n)==="y",u=Cy.has(i)?-1:1,d=r&&s?-1:1,m=zn(t,e);let{mainAxis:h,crossAxis:f,alignmentAxis:w}=typeof m=="number"?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return o&&typeof w=="number"&&(f=o==="end"?w*-1:w),s?{x:f*d,y:h*u}:{x:h*u,y:f*d}}const u2=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,a;const{x:l,y:r,placement:i,middlewareData:o}=t,s=await c2(t,e);return i===((n=o.offset)==null?void 0:n.placement)&&(a=o.arrow)!=null&&a.alignmentOffset?{}:{x:l+s.x,y:r+s.y,data:{...s,placement:i}}}}},f2=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:a,placement:l}=t,{mainAxis:r=!0,crossAxis:i=!1,limiter:o={fn:x=>{let{x:v,y:g}=x;return{x:v,y:g}}},...s}=zn(e,t),u={x:n,y:a},d=await yi(t,s),m=tn(Un(l)),h=vd(m);let f=u[h],w=u[m];if(r){const x=h==="y"?"top":"left",v=h==="y"?"bottom":"right",g=f+d[x],b=f-d[v];f=tf(g,f,b)}if(i){const x=m==="y"?"top":"left",v=m==="y"?"bottom":"right",g=w+d[x],b=w-d[v];w=tf(g,w,b)}const y=o.fn({...t,[h]:f,[m]:w});return{...y,data:{x:y.x-n,y:y.y-a,enabled:{[h]:r,[m]:i}}}}}},d2=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:a,placement:l,rects:r,middlewareData:i}=t,{offset:o=0,mainAxis:s=!0,crossAxis:u=!0}=zn(e,t),d={x:n,y:a},m=tn(l),h=vd(m);let f=d[h],w=d[m];const y=zn(o,t),x=typeof y=="number"?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(s){const b=h==="y"?"height":"width",S=r.reference[h]-r.floating[b]+x.mainAxis,E=r.reference[h]+r.reference[b]-x.mainAxis;f<S?f=S:f>E&&(f=E)}if(u){var v,g;const b=h==="y"?"width":"height",S=Cy.has(Un(l)),E=r.reference[m]-r.floating[b]+(S&&((v=i.offset)==null?void 0:v[m])||0)+(S?0:x.crossAxis),T=r.reference[m]+r.reference[b]+(S?0:((g=i.offset)==null?void 0:g[m])||0)-(S?x.crossAxis:0);w<E?w=E:w>T&&(w=T)}return{[h]:f,[m]:w}}}},h2=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,a;const{placement:l,rects:r,platform:i,elements:o}=t,{apply:s=()=>{},...u}=zn(e,t),d=await yi(t,u),m=Un(l),h=xr(l),f=tn(l)==="y",{width:w,height:y}=r.floating;let x,v;m==="top"||m==="bottom"?(x=m,v=h===(await(i.isRTL==null?void 0:i.isRTL(o.floating))?"start":"end")?"left":"right"):(v=m,x=h==="end"?"top":"bottom");const g=y-d.top-d.bottom,b=w-d.left-d.right,S=Aa(y-d[x],g),E=Aa(w-d[v],b),T=!t.middlewareData.shift;let A=S,C=E;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(C=b),(a=t.middlewareData.shift)!=null&&a.enabled.y&&(A=g),T&&!h){const M=ht(d.left,0),B=ht(d.right,0),L=ht(d.top,0),$=ht(d.bottom,0);f?C=w-2*(M!==0||B!==0?M+B:ht(d.left,d.right)):A=y-2*(L!==0||$!==0?L+$:ht(d.top,d.bottom))}await s({...t,availableWidth:C,availableHeight:A});const O=await i.getDimensions(o.floating);return w!==O.width||y!==O.height?{reset:{rects:!0}}:{}}}};function Ys(){return typeof window<"u"}function Sr(e){return jy(e)?(e.nodeName||"").toLowerCase():"#document"}function bt(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function un(e){var t;return(t=(jy(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function jy(e){return Ys()?e instanceof Node||e instanceof bt(e).Node:!1}function Vt(e){return Ys()?e instanceof Element||e instanceof bt(e).Element:!1}function on(e){return Ys()?e instanceof HTMLElement||e instanceof bt(e).HTMLElement:!1}function Rm(e){return!Ys()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof bt(e).ShadowRoot}const m2=new Set(["inline","contents"]);function Yi(e){const{overflow:t,overflowX:n,overflowY:a,display:l}=Qt(e);return/auto|scroll|overlay|hidden|clip/.test(t+a+n)&&!m2.has(l)}const p2=new Set(["table","td","th"]);function v2(e){return p2.has(Sr(e))}const g2=[":popover-open",":modal"];function Vs(e){return g2.some(t=>{try{return e.matches(t)}catch{return!1}})}const y2=["transform","translate","scale","rotate","perspective"],b2=["transform","translate","scale","rotate","perspective","filter"],x2=["paint","layout","strict","content"];function bd(e){const t=xd(),n=Vt(e)?Qt(e):e;return y2.some(a=>n[a]?n[a]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||b2.some(a=>(n.willChange||"").includes(a))||x2.some(a=>(n.contain||"").includes(a))}function S2(e){let t=Ta(e);for(;on(t)&&!ur(t);){if(bd(t))return t;if(Vs(t))return null;t=Ta(t)}return null}function xd(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const w2=new Set(["html","body","#document"]);function ur(e){return w2.has(Sr(e))}function Qt(e){return bt(e).getComputedStyle(e)}function Qs(e){return Vt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Ta(e){if(Sr(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Rm(e)&&e.host||un(e);return Rm(t)?t.host:t}function Ry(e){const t=Ta(e);return ur(t)?e.ownerDocument?e.ownerDocument.body:e.body:on(t)&&Yi(t)?t:Ry(t)}function bi(e,t,n){var a;t===void 0&&(t=[]),n===void 0&&(n=!0);const l=Ry(e),r=l===((a=e.ownerDocument)==null?void 0:a.body),i=bt(l);if(r){const o=af(i);return t.concat(i,i.visualViewport||[],Yi(l)?l:[],o&&n?bi(o):[])}return t.concat(l,bi(l,[],n))}function af(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Oy(e){const t=Qt(e);let n=parseFloat(t.width)||0,a=parseFloat(t.height)||0;const l=on(e),r=l?e.offsetWidth:n,i=l?e.offsetHeight:a,o=ds(n)!==r||ds(a)!==i;return o&&(n=r,a=i),{width:n,height:a,$:o}}function Sd(e){return Vt(e)?e:e.contextElement}function Gl(e){const t=Sd(e);if(!on(t))return rn(1);const n=t.getBoundingClientRect(),{width:a,height:l,$:r}=Oy(t);let i=(r?ds(n.width):n.width)/a,o=(r?ds(n.height):n.height)/l;return(!i||!Number.isFinite(i))&&(i=1),(!o||!Number.isFinite(o))&&(o=1),{x:i,y:o}}const E2=rn(0);function My(e){const t=bt(e);return!xd()||!t.visualViewport?E2:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function A2(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==bt(e)?!1:t}function Ia(e,t,n,a){t===void 0&&(t=!1),n===void 0&&(n=!1);const l=e.getBoundingClientRect(),r=Sd(e);let i=rn(1);t&&(a?Vt(a)&&(i=Gl(a)):i=Gl(e));const o=A2(r,n,a)?My(r):rn(0);let s=(l.left+o.x)/i.x,u=(l.top+o.y)/i.y,d=l.width/i.x,m=l.height/i.y;if(r){const h=bt(r),f=a&&Vt(a)?bt(a):a;let w=h,y=af(w);for(;y&&a&&f!==w;){const x=Gl(y),v=y.getBoundingClientRect(),g=Qt(y),b=v.left+(y.clientLeft+parseFloat(g.paddingLeft))*x.x,S=v.top+(y.clientTop+parseFloat(g.paddingTop))*x.y;s*=x.x,u*=x.y,d*=x.x,m*=x.y,s+=b,u+=S,w=bt(y),y=af(w)}}return ms({width:d,height:m,x:s,y:u})}function wd(e,t){const n=Qs(e).scrollLeft;return t?t.left+n:Ia(un(e)).left+n}function Dy(e,t,n){n===void 0&&(n=!1);const a=e.getBoundingClientRect(),l=a.left+t.scrollLeft-(n?0:wd(e,a)),r=a.top+t.scrollTop;return{x:l,y:r}}function T2(e){let{elements:t,rect:n,offsetParent:a,strategy:l}=e;const r=l==="fixed",i=un(a),o=t?Vs(t.floating):!1;if(a===i||o&&r)return n;let s={scrollLeft:0,scrollTop:0},u=rn(1);const d=rn(0),m=on(a);if((m||!m&&!r)&&((Sr(a)!=="body"||Yi(i))&&(s=Qs(a)),on(a))){const f=Ia(a);u=Gl(a),d.x=f.x+a.clientLeft,d.y=f.y+a.clientTop}const h=i&&!m&&!r?Dy(i,s,!0):rn(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+d.x+h.x,y:n.y*u.y-s.scrollTop*u.y+d.y+h.y}}function N2(e){return Array.from(e.getClientRects())}function C2(e){const t=un(e),n=Qs(e),a=e.ownerDocument.body,l=ht(t.scrollWidth,t.clientWidth,a.scrollWidth,a.clientWidth),r=ht(t.scrollHeight,t.clientHeight,a.scrollHeight,a.clientHeight);let i=-n.scrollLeft+wd(e);const o=-n.scrollTop;return Qt(a).direction==="rtl"&&(i+=ht(t.clientWidth,a.clientWidth)-l),{width:l,height:r,x:i,y:o}}function j2(e,t){const n=bt(e),a=un(e),l=n.visualViewport;let r=a.clientWidth,i=a.clientHeight,o=0,s=0;if(l){r=l.width,i=l.height;const u=xd();(!u||u&&t==="fixed")&&(o=l.offsetLeft,s=l.offsetTop)}return{width:r,height:i,x:o,y:s}}const R2=new Set(["absolute","fixed"]);function O2(e,t){const n=Ia(e,!0,t==="fixed"),a=n.top+e.clientTop,l=n.left+e.clientLeft,r=on(e)?Gl(e):rn(1),i=e.clientWidth*r.x,o=e.clientHeight*r.y,s=l*r.x,u=a*r.y;return{width:i,height:o,x:s,y:u}}function Om(e,t,n){let a;if(t==="viewport")a=j2(e,n);else if(t==="document")a=C2(un(e));else if(Vt(t))a=O2(t,n);else{const l=My(e);a={x:t.x-l.x,y:t.y-l.y,width:t.width,height:t.height}}return ms(a)}function _y(e,t){const n=Ta(e);return n===t||!Vt(n)||ur(n)?!1:Qt(n).position==="fixed"||_y(n,t)}function M2(e,t){const n=t.get(e);if(n)return n;let a=bi(e,[],!1).filter(o=>Vt(o)&&Sr(o)!=="body"),l=null;const r=Qt(e).position==="fixed";let i=r?Ta(e):e;for(;Vt(i)&&!ur(i);){const o=Qt(i),s=bd(i);!s&&o.position==="fixed"&&(l=null),(r?!s&&!l:!s&&o.position==="static"&&!!l&&R2.has(l.position)||Yi(i)&&!s&&_y(e,i))?a=a.filter(d=>d!==i):l=o,i=Ta(i)}return t.set(e,a),a}function D2(e){let{element:t,boundary:n,rootBoundary:a,strategy:l}=e;const i=[...n==="clippingAncestors"?Vs(t)?[]:M2(t,this._c):[].concat(n),a],o=i[0],s=i.reduce((u,d)=>{const m=Om(t,d,l);return u.top=ht(m.top,u.top),u.right=Aa(m.right,u.right),u.bottom=Aa(m.bottom,u.bottom),u.left=ht(m.left,u.left),u},Om(t,o,l));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}}function _2(e){const{width:t,height:n}=Oy(e);return{width:t,height:n}}function z2(e,t,n){const a=on(t),l=un(t),r=n==="fixed",i=Ia(e,!0,r,t);let o={scrollLeft:0,scrollTop:0};const s=rn(0);function u(){s.x=wd(l)}if(a||!a&&!r)if((Sr(t)!=="body"||Yi(l))&&(o=Qs(t)),a){const f=Ia(t,!0,r,t);s.x=f.x+t.clientLeft,s.y=f.y+t.clientTop}else l&&u();r&&!a&&l&&u();const d=l&&!a&&!r?Dy(l,o):rn(0),m=i.left+o.scrollLeft-s.x-d.x,h=i.top+o.scrollTop-s.y-d.y;return{x:m,y:h,width:i.width,height:i.height}}function Qc(e){return Qt(e).position==="static"}function Mm(e,t){if(!on(e)||Qt(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return un(e)===n&&(n=n.ownerDocument.body),n}function zy(e,t){const n=bt(e);if(Vs(e))return n;if(!on(e)){let l=Ta(e);for(;l&&!ur(l);){if(Vt(l)&&!Qc(l))return l;l=Ta(l)}return n}let a=Mm(e,t);for(;a&&v2(a)&&Qc(a);)a=Mm(a,t);return a&&ur(a)&&Qc(a)&&!bd(a)?n:a||S2(e)||n}const U2=async function(e){const t=this.getOffsetParent||zy,n=this.getDimensions,a=await n(e.floating);return{reference:z2(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:a.width,height:a.height}}};function L2(e){return Qt(e).direction==="rtl"}const B2={convertOffsetParentRelativeRectToViewportRelativeRect:T2,getDocumentElement:un,getClippingRect:D2,getOffsetParent:zy,getElementRects:U2,getClientRects:N2,getDimensions:_2,getScale:Gl,isElement:Vt,isRTL:L2};function Uy(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function H2(e,t){let n=null,a;const l=un(e);function r(){var o;clearTimeout(a),(o=n)==null||o.disconnect(),n=null}function i(o,s){o===void 0&&(o=!1),s===void 0&&(s=1),r();const u=e.getBoundingClientRect(),{left:d,top:m,width:h,height:f}=u;if(o||t(),!h||!f)return;const w=uo(m),y=uo(l.clientWidth-(d+h)),x=uo(l.clientHeight-(m+f)),v=uo(d),b={rootMargin:-w+"px "+-y+"px "+-x+"px "+-v+"px",threshold:ht(0,Aa(1,s))||1};let S=!0;function E(T){const A=T[0].intersectionRatio;if(A!==s){if(!S)return i();A?i(!1,A):a=setTimeout(()=>{i(!1,1e-7)},1e3)}A===1&&!Uy(u,e.getBoundingClientRect())&&i(),S=!1}try{n=new IntersectionObserver(E,{...b,root:l.ownerDocument})}catch{n=new IntersectionObserver(E,b)}n.observe(e)}return i(!0),r}function k2(e,t,n,a){a===void 0&&(a={});const{ancestorScroll:l=!0,ancestorResize:r=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:o=typeof IntersectionObserver=="function",animationFrame:s=!1}=a,u=Sd(e),d=l||r?[...u?bi(u):[],...bi(t)]:[];d.forEach(v=>{l&&v.addEventListener("scroll",n,{passive:!0}),r&&v.addEventListener("resize",n)});const m=u&&o?H2(u,n):null;let h=-1,f=null;i&&(f=new ResizeObserver(v=>{let[g]=v;g&&g.target===u&&f&&(f.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var b;(b=f)==null||b.observe(t)})),n()}),u&&!s&&f.observe(u),f.observe(t));let w,y=s?Ia(e):null;s&&x();function x(){const v=Ia(e);y&&!Uy(y,v)&&n(),y=v,w=requestAnimationFrame(x)}return n(),()=>{var v;d.forEach(g=>{l&&g.removeEventListener("scroll",n),r&&g.removeEventListener("resize",n)}),m==null||m(),(v=f)==null||v.disconnect(),f=null,s&&cancelAnimationFrame(w)}}const q2=u2,G2=f2,Y2=o2,V2=h2,Q2=s2,Dm=i2,X2=d2,P2=(e,t,n)=>{const a=new Map,l={platform:B2,...n},r={...l.platform,_c:a};return r2(e,t,{...l,platform:r})};var Z2=typeof document<"u",K2=function(){},Mo=Z2?p.useLayoutEffect:K2;function ps(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,a,l;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(a=n;a--!==0;)if(!ps(e[a],t[a]))return!1;return!0}if(l=Object.keys(e),n=l.length,n!==Object.keys(t).length)return!1;for(a=n;a--!==0;)if(!{}.hasOwnProperty.call(t,l[a]))return!1;for(a=n;a--!==0;){const r=l[a];if(!(r==="_owner"&&e.$$typeof)&&!ps(e[r],t[r]))return!1}return!0}return e!==e&&t!==t}function Ly(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function _m(e,t){const n=Ly(e);return Math.round(t*n)/n}function Xc(e){const t=p.useRef(e);return Mo(()=>{t.current=e}),t}function F2(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:a=[],platform:l,elements:{reference:r,floating:i}={},transform:o=!0,whileElementsMounted:s,open:u}=e,[d,m]=p.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,f]=p.useState(a);ps(h,a)||f(a);const[w,y]=p.useState(null),[x,v]=p.useState(null),g=p.useCallback(_=>{_!==T.current&&(T.current=_,y(_))},[]),b=p.useCallback(_=>{_!==A.current&&(A.current=_,v(_))},[]),S=r||w,E=i||x,T=p.useRef(null),A=p.useRef(null),C=p.useRef(d),O=s!=null,M=Xc(s),B=Xc(l),L=Xc(u),$=p.useCallback(()=>{if(!T.current||!A.current)return;const _={placement:t,strategy:n,middleware:h};B.current&&(_.platform=B.current),P2(T.current,A.current,_).then(z=>{const k={...z,isPositioned:L.current!==!1};Z.current&&!ps(C.current,k)&&(C.current=k,Ns.flushSync(()=>{m(k)}))})},[h,t,n,B,L]);Mo(()=>{u===!1&&C.current.isPositioned&&(C.current.isPositioned=!1,m(_=>({..._,isPositioned:!1})))},[u]);const Z=p.useRef(!1);Mo(()=>(Z.current=!0,()=>{Z.current=!1}),[]),Mo(()=>{if(S&&(T.current=S),E&&(A.current=E),S&&E){if(M.current)return M.current(S,E,$);$()}},[S,E,$,M,O]);const ce=p.useMemo(()=>({reference:T,floating:A,setReference:g,setFloating:b}),[g,b]),R=p.useMemo(()=>({reference:S,floating:E}),[S,E]),U=p.useMemo(()=>{const _={position:n,left:0,top:0};if(!R.floating)return _;const z=_m(R.floating,d.x),k=_m(R.floating,d.y);return o?{..._,transform:"translate("+z+"px, "+k+"px)",...Ly(R.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:z,top:k}},[n,o,R.floating,d.x,d.y]);return p.useMemo(()=>({...d,update:$,refs:ce,elements:R,floatingStyles:U}),[d,$,ce,R,U])}const J2=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:a,padding:l}=typeof e=="function"?e(n):e;return a&&t(a)?a.current!=null?Dm({element:a.current,padding:l}).fn(n):{}:a?Dm({element:a,padding:l}).fn(n):{}}}},$2=(e,t)=>({...q2(e),options:[e,t]}),W2=(e,t)=>({...G2(e),options:[e,t]}),I2=(e,t)=>({...X2(e),options:[e,t]}),eE=(e,t)=>({...Y2(e),options:[e,t]}),tE=(e,t)=>({...V2(e),options:[e,t]}),nE=(e,t)=>({...Q2(e),options:[e,t]}),aE=(e,t)=>({...J2(e),options:[e,t]});var lE="Arrow",By=p.forwardRef((e,t)=>{const{children:n,width:a=10,height:l=5,...r}=e;return c.jsx(pe.svg,{...r,ref:t,width:a,height:l,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:c.jsx("polygon",{points:"0,0 30,0 15,10"})})});By.displayName=lE;var rE=By;function iE(e){const[t,n]=p.useState(void 0);return _n(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const a=new ResizeObserver(l=>{if(!Array.isArray(l)||!l.length)return;const r=l[0];let i,o;if("borderBoxSize"in r){const s=r.borderBoxSize,u=Array.isArray(s)?s[0]:s;i=u.inlineSize,o=u.blockSize}else i=e.offsetWidth,o=e.offsetHeight;n({width:i,height:o})});return a.observe(e,{box:"border-box"}),()=>a.unobserve(e)}else n(void 0)},[e]),t}var Hy="Popper",[ky,qy]=rl(Hy),[uj,Gy]=ky(Hy),Yy="PopperAnchor",Vy=p.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:a,...l}=e,r=Gy(Yy,n),i=p.useRef(null),o=Le(t,i);return p.useEffect(()=>{r.onAnchorChange((a==null?void 0:a.current)||i.current)}),a?null:c.jsx(pe.div,{...l,ref:o})});Vy.displayName=Yy;var Ed="PopperContent",[oE,sE]=ky(Ed),Qy=p.forwardRef((e,t)=>{var de,il,Hn,ja,kn,ol;const{__scopePopper:n,side:a="bottom",sideOffset:l=0,align:r="center",alignOffset:i=0,arrowPadding:o=0,avoidCollisions:s=!0,collisionBoundary:u=[],collisionPadding:d=0,sticky:m="partial",hideWhenDetached:h=!1,updatePositionStrategy:f="optimized",onPlaced:w,...y}=e,x=Gy(Ed,n),[v,g]=p.useState(null),b=Le(t,qn=>g(qn)),[S,E]=p.useState(null),T=iE(S),A=(T==null?void 0:T.width)??0,C=(T==null?void 0:T.height)??0,O=a+(r!=="center"?"-"+r:""),M=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},B=Array.isArray(u)?u:[u],L=B.length>0,$={padding:M,boundary:B.filter(uE),altBoundary:L},{refs:Z,floatingStyles:ce,placement:R,isPositioned:U,middlewareData:_}=F2({strategy:"fixed",placement:O,whileElementsMounted:(...qn)=>k2(...qn,{animationFrame:f==="always"}),elements:{reference:x.anchor},middleware:[$2({mainAxis:l+C,alignmentAxis:i}),s&&W2({mainAxis:!0,crossAxis:!1,limiter:m==="partial"?I2():void 0,...$}),s&&eE({...$}),tE({...$,apply:({elements:qn,rects:Qi,availableWidth:lc,availableHeight:Xi})=>{const{width:rc,height:Er}=Qi.reference,sl=qn.floating.style;sl.setProperty("--radix-popper-available-width",`${lc}px`),sl.setProperty("--radix-popper-available-height",`${Xi}px`),sl.setProperty("--radix-popper-anchor-width",`${rc}px`),sl.setProperty("--radix-popper-anchor-height",`${Er}px`)}}),S&&aE({element:S,padding:o}),fE({arrowWidth:A,arrowHeight:C}),h&&nE({strategy:"referenceHidden",...$})]}),[z,k]=Zy(R),Ee=it(w);_n(()=>{U&&(Ee==null||Ee())},[U,Ee]);const K=(de=_.arrow)==null?void 0:de.x,I=(il=_.arrow)==null?void 0:il.y,J=((Hn=_.arrow)==null?void 0:Hn.centerOffset)!==0,[Re,Bn]=p.useState();return _n(()=>{v&&Bn(window.getComputedStyle(v).zIndex)},[v]),c.jsx("div",{ref:Z.setFloating,"data-radix-popper-content-wrapper":"",style:{...ce,transform:U?ce.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Re,"--radix-popper-transform-origin":[(ja=_.transformOrigin)==null?void 0:ja.x,(kn=_.transformOrigin)==null?void 0:kn.y].join(" "),...((ol=_.hide)==null?void 0:ol.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:c.jsx(oE,{scope:n,placedSide:z,onArrowChange:E,arrowX:K,arrowY:I,shouldHideArrow:J,children:c.jsx(pe.div,{"data-side":z,"data-align":k,...y,ref:b,style:{...y.style,animation:U?void 0:"none"}})})})});Qy.displayName=Ed;var Xy="PopperArrow",cE={top:"bottom",right:"left",bottom:"top",left:"right"},Py=p.forwardRef(function(t,n){const{__scopePopper:a,...l}=t,r=sE(Xy,a),i=cE[r.placedSide];return c.jsx("span",{ref:r.onArrowChange,style:{position:"absolute",left:r.arrowX,top:r.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[r.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[r.placedSide],visibility:r.shouldHideArrow?"hidden":void 0},children:c.jsx(rE,{...l,ref:n,style:{...l.style,display:"block"}})})});Py.displayName=Xy;function uE(e){return e!==null}var fE=e=>({name:"transformOrigin",options:e,fn(t){var x,v,g;const{placement:n,rects:a,middlewareData:l}=t,i=((x=l.arrow)==null?void 0:x.centerOffset)!==0,o=i?0:e.arrowWidth,s=i?0:e.arrowHeight,[u,d]=Zy(n),m={start:"0%",center:"50%",end:"100%"}[d],h=(((v=l.arrow)==null?void 0:v.x)??0)+o/2,f=(((g=l.arrow)==null?void 0:g.y)??0)+s/2;let w="",y="";return u==="bottom"?(w=i?m:`${h}px`,y=`${-s}px`):u==="top"?(w=i?m:`${h}px`,y=`${a.floating.height+s}px`):u==="right"?(w=`${-s}px`,y=i?m:`${f}px`):u==="left"&&(w=`${a.floating.width+s}px`,y=i?m:`${f}px`),{data:{x:w,y}}}});function Zy(e){const[t,n="center"]=e.split("-");return[t,n]}var dE=Vy,hE=Qy,mE=Py,pE="Portal",Ky=p.forwardRef((e,t)=>{var o;const{container:n,...a}=e,[l,r]=p.useState(!1);_n(()=>r(!0),[]);const i=n||l&&((o=globalThis==null?void 0:globalThis.document)==null?void 0:o.body);return i?qp.createPortal(c.jsx(pe.div,{...a,ref:t}),i):null});Ky.displayName=pE;function vE(e,t){return p.useReducer((n,a)=>t[n][a]??n,e)}var fn=e=>{const{present:t,children:n}=e,a=gE(t),l=typeof n=="function"?n({present:a.isPresent}):p.Children.only(n),r=Le(a.ref,yE(l));return typeof n=="function"||a.isPresent?p.cloneElement(l,{ref:r}):null};fn.displayName="Presence";function gE(e){const[t,n]=p.useState(),a=p.useRef(null),l=p.useRef(e),r=p.useRef("none"),i=e?"mounted":"unmounted",[o,s]=vE(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return p.useEffect(()=>{const u=fo(a.current);r.current=o==="mounted"?u:"none"},[o]),_n(()=>{const u=a.current,d=l.current;if(d!==e){const h=r.current,f=fo(u);e?s("MOUNT"):f==="none"||(u==null?void 0:u.display)==="none"?s("UNMOUNT"):s(d&&h!==f?"ANIMATION_OUT":"UNMOUNT"),l.current=e}},[e,s]),_n(()=>{if(t){let u;const d=t.ownerDocument.defaultView??window,m=f=>{const y=fo(a.current).includes(f.animationName);if(f.target===t&&y&&(s("ANIMATION_END"),!l.current)){const x=t.style.animationFillMode;t.style.animationFillMode="forwards",u=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=x)})}},h=f=>{f.target===t&&(r.current=fo(a.current))};return t.addEventListener("animationstart",h),t.addEventListener("animationcancel",m),t.addEventListener("animationend",m),()=>{d.clearTimeout(u),t.removeEventListener("animationstart",h),t.removeEventListener("animationcancel",m),t.removeEventListener("animationend",m)}}else s("ANIMATION_END")},[t,s]),{isPresent:["mounted","unmountSuspended"].includes(o),ref:p.useCallback(u=>{a.current=u?getComputedStyle(u):null,n(u)},[])}}function fo(e){return(e==null?void 0:e.animationName)||"none"}function yE(e){var a,l;let t=(a=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:a.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(l=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:l.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var bE=xf[" useInsertionEffect ".trim().toString()]||_n;function Ad({prop:e,defaultProp:t,onChange:n=()=>{},caller:a}){const[l,r,i]=xE({defaultProp:t,onChange:n}),o=e!==void 0,s=o?e:l;{const d=p.useRef(e!==void 0);p.useEffect(()=>{const m=d.current;m!==o&&console.warn(`${a} is changing from ${m?"controlled":"uncontrolled"} to ${o?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),d.current=o},[o,a])}const u=p.useCallback(d=>{var m;if(o){const h=SE(d)?d(e):d;h!==e&&((m=i.current)==null||m.call(i,h))}else r(d)},[o,e,r,i]);return[s,u]}function xE({defaultProp:e,onChange:t}){const[n,a]=p.useState(e),l=p.useRef(n),r=p.useRef(t);return bE(()=>{r.current=t},[t]),p.useEffect(()=>{var i;l.current!==n&&((i=r.current)==null||i.call(r,n),l.current=n)},[n,l]),[n,a,r]}function SE(e){return typeof e=="function"}var wE=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),EE="VisuallyHidden",Fy=p.forwardRef((e,t)=>c.jsx(pe.span,{...e,ref:t,style:{...wE,...e.style}}));Fy.displayName=EE;var AE=Fy,[Xs,fj]=rl("Tooltip",[qy]),Td=qy(),Jy="TooltipProvider",TE=700,zm="tooltip.open",[NE,$y]=Xs(Jy),Wy=e=>{const{__scopeTooltip:t,delayDuration:n=TE,skipDelayDuration:a=300,disableHoverableContent:l=!1,children:r}=e,i=p.useRef(!0),o=p.useRef(!1),s=p.useRef(0);return p.useEffect(()=>{const u=s.current;return()=>window.clearTimeout(u)},[]),c.jsx(NE,{scope:t,isOpenDelayedRef:i,delayDuration:n,onOpen:p.useCallback(()=>{window.clearTimeout(s.current),i.current=!1},[]),onClose:p.useCallback(()=>{window.clearTimeout(s.current),s.current=window.setTimeout(()=>i.current=!0,a)},[a]),isPointerInTransitRef:o,onPointerInTransitChange:p.useCallback(u=>{o.current=u},[]),disableHoverableContent:l,children:r})};Wy.displayName=Jy;var Iy="Tooltip",[dj,Ps]=Xs(Iy),lf="TooltipTrigger",CE=p.forwardRef((e,t)=>{const{__scopeTooltip:n,...a}=e,l=Ps(lf,n),r=$y(lf,n),i=Td(n),o=p.useRef(null),s=Le(t,o,l.onTriggerChange),u=p.useRef(!1),d=p.useRef(!1),m=p.useCallback(()=>u.current=!1,[]);return p.useEffect(()=>()=>document.removeEventListener("pointerup",m),[m]),c.jsx(dE,{asChild:!0,...i,children:c.jsx(pe.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...a,ref:s,onPointerMove:ne(e.onPointerMove,h=>{h.pointerType!=="touch"&&!d.current&&!r.isPointerInTransitRef.current&&(l.onTriggerEnter(),d.current=!0)}),onPointerLeave:ne(e.onPointerLeave,()=>{l.onTriggerLeave(),d.current=!1}),onPointerDown:ne(e.onPointerDown,()=>{l.open&&l.onClose(),u.current=!0,document.addEventListener("pointerup",m,{once:!0})}),onFocus:ne(e.onFocus,()=>{u.current||l.onOpen()}),onBlur:ne(e.onBlur,l.onClose),onClick:ne(e.onClick,l.onClose)})})});CE.displayName=lf;var jE="TooltipPortal",[hj,RE]=Xs(jE,{forceMount:void 0}),fr="TooltipContent",e0=p.forwardRef((e,t)=>{const n=RE(fr,e.__scopeTooltip),{forceMount:a=n.forceMount,side:l="top",...r}=e,i=Ps(fr,e.__scopeTooltip);return c.jsx(fn,{present:a||i.open,children:i.disableHoverableContent?c.jsx(t0,{side:l,...r,ref:t}):c.jsx(OE,{side:l,...r,ref:t})})}),OE=p.forwardRef((e,t)=>{const n=Ps(fr,e.__scopeTooltip),a=$y(fr,e.__scopeTooltip),l=p.useRef(null),r=Le(t,l),[i,o]=p.useState(null),{trigger:s,onClose:u}=n,d=l.current,{onPointerInTransitChange:m}=a,h=p.useCallback(()=>{o(null),m(!1)},[m]),f=p.useCallback((w,y)=>{const x=w.currentTarget,v={x:w.clientX,y:w.clientY},g=UE(v,x.getBoundingClientRect()),b=LE(v,g),S=BE(y.getBoundingClientRect()),E=kE([...b,...S]);o(E),m(!0)},[m]);return p.useEffect(()=>()=>h(),[h]),p.useEffect(()=>{if(s&&d){const w=x=>f(x,d),y=x=>f(x,s);return s.addEventListener("pointerleave",w),d.addEventListener("pointerleave",y),()=>{s.removeEventListener("pointerleave",w),d.removeEventListener("pointerleave",y)}}},[s,d,f,h]),p.useEffect(()=>{if(i){const w=y=>{const x=y.target,v={x:y.clientX,y:y.clientY},g=(s==null?void 0:s.contains(x))||(d==null?void 0:d.contains(x)),b=!HE(v,i);g?h():b&&(h(),u())};return document.addEventListener("pointermove",w),()=>document.removeEventListener("pointermove",w)}},[s,d,i,u,h]),c.jsx(t0,{...e,ref:r})}),[ME,DE]=Xs(Iy,{isInside:!1}),_E=Dw("TooltipContent"),t0=p.forwardRef((e,t)=>{const{__scopeTooltip:n,children:a,"aria-label":l,onEscapeKeyDown:r,onPointerDownOutside:i,...o}=e,s=Ps(fr,n),u=Td(n),{onClose:d}=s;return p.useEffect(()=>(document.addEventListener(zm,d),()=>document.removeEventListener(zm,d)),[d]),p.useEffect(()=>{if(s.trigger){const m=h=>{const f=h.target;f!=null&&f.contains(s.trigger)&&d()};return window.addEventListener("scroll",m,{capture:!0}),()=>window.removeEventListener("scroll",m,{capture:!0})}},[s.trigger,d]),c.jsx(pd,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:r,onPointerDownOutside:i,onFocusOutside:m=>m.preventDefault(),onDismiss:d,children:c.jsxs(hE,{"data-state":s.stateAttribute,...u,...o,ref:t,style:{...o.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[c.jsx(_E,{children:a}),c.jsx(ME,{scope:n,isInside:!0,children:c.jsx(AE,{id:s.contentId,role:"tooltip",children:l||a})})]})})});e0.displayName=fr;var n0="TooltipArrow",zE=p.forwardRef((e,t)=>{const{__scopeTooltip:n,...a}=e,l=Td(n);return DE(n0,n).isInside?null:c.jsx(mE,{...l,...a,ref:t})});zE.displayName=n0;function UE(e,t){const n=Math.abs(t.top-e.y),a=Math.abs(t.bottom-e.y),l=Math.abs(t.right-e.x),r=Math.abs(t.left-e.x);switch(Math.min(n,a,l,r)){case r:return"left";case l:return"right";case n:return"top";case a:return"bottom";default:throw new Error("unreachable")}}function LE(e,t,n=5){const a=[];switch(t){case"top":a.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":a.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":a.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":a.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return a}function BE(e){const{top:t,right:n,bottom:a,left:l}=e;return[{x:l,y:t},{x:n,y:t},{x:n,y:a},{x:l,y:a}]}function HE(e,t){const{x:n,y:a}=e;let l=!1;for(let r=0,i=t.length-1;r<t.length;i=r++){const o=t[r],s=t[i],u=o.x,d=o.y,m=s.x,h=s.y;d>a!=h>a&&n<(m-u)*(a-d)/(h-d)+u&&(l=!l)}return l}function kE(e){const t=e.slice();return t.sort((n,a)=>n.x<a.x?-1:n.x>a.x?1:n.y<a.y?-1:n.y>a.y?1:0),qE(t)}function qE(e){if(e.length<=1)return e.slice();const t=[];for(let a=0;a<e.length;a++){const l=e[a];for(;t.length>=2;){const r=t[t.length-1],i=t[t.length-2];if((r.x-i.x)*(l.y-i.y)>=(r.y-i.y)*(l.x-i.x))t.pop();else break}t.push(l)}t.pop();const n=[];for(let a=e.length-1;a>=0;a--){const l=e[a];for(;n.length>=2;){const r=n[n.length-1],i=n[n.length-2];if((r.x-i.x)*(l.y-i.y)>=(r.y-i.y)*(l.x-i.x))n.pop();else break}n.push(l)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var GE=Wy,a0=e0;function l0(e){var t,n,a="";if(typeof e=="string"||typeof e=="number")a+=e;else if(typeof e=="object")if(Array.isArray(e)){var l=e.length;for(t=0;t<l;t++)e[t]&&(n=l0(e[t]))&&(a&&(a+=" "),a+=n)}else for(n in e)e[n]&&(a&&(a+=" "),a+=n);return a}function r0(){for(var e,t,n=0,a="",l=arguments.length;n<l;n++)(e=arguments[n])&&(t=l0(e))&&(a&&(a+=" "),a+=t);return a}const Nd="-",YE=e=>{const t=QE(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:a}=e;return{getClassGroupId:i=>{const o=i.split(Nd);return o[0]===""&&o.length!==1&&o.shift(),i0(o,t)||VE(i)},getConflictingClassGroupIds:(i,o)=>{const s=n[i]||[];return o&&a[i]?[...s,...a[i]]:s}}},i0=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],a=t.nextPart.get(n),l=a?i0(e.slice(1),a):void 0;if(l)return l;if(t.validators.length===0)return;const r=e.join(Nd);return(i=t.validators.find(({validator:o})=>o(r)))==null?void 0:i.classGroupId},Um=/^\[(.+)\]$/,VE=e=>{if(Um.test(e)){const t=Um.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},QE=e=>{const{theme:t,prefix:n}=e,a={nextPart:new Map,validators:[]};return PE(Object.entries(e.classGroups),n).forEach(([r,i])=>{rf(i,a,r,t)}),a},rf=(e,t,n,a)=>{e.forEach(l=>{if(typeof l=="string"){const r=l===""?t:Lm(t,l);r.classGroupId=n;return}if(typeof l=="function"){if(XE(l)){rf(l(a),t,n,a);return}t.validators.push({validator:l,classGroupId:n});return}Object.entries(l).forEach(([r,i])=>{rf(i,Lm(t,r),n,a)})})},Lm=(e,t)=>{let n=e;return t.split(Nd).forEach(a=>{n.nextPart.has(a)||n.nextPart.set(a,{nextPart:new Map,validators:[]}),n=n.nextPart.get(a)}),n},XE=e=>e.isThemeGetter,PE=(e,t)=>t?e.map(([n,a])=>{const l=a.map(r=>typeof r=="string"?t+r:typeof r=="object"?Object.fromEntries(Object.entries(r).map(([i,o])=>[t+i,o])):r);return[n,l]}):e,ZE=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,a=new Map;const l=(r,i)=>{n.set(r,i),t++,t>e&&(t=0,a=n,n=new Map)};return{get(r){let i=n.get(r);if(i!==void 0)return i;if((i=a.get(r))!==void 0)return l(r,i),i},set(r,i){n.has(r)?n.set(r,i):l(r,i)}}},o0="!",KE=e=>{const{separator:t,experimentalParseClassName:n}=e,a=t.length===1,l=t[0],r=t.length,i=o=>{const s=[];let u=0,d=0,m;for(let x=0;x<o.length;x++){let v=o[x];if(u===0){if(v===l&&(a||o.slice(x,x+r)===t)){s.push(o.slice(d,x)),d=x+r;continue}if(v==="/"){m=x;continue}}v==="["?u++:v==="]"&&u--}const h=s.length===0?o:o.substring(d),f=h.startsWith(o0),w=f?h.substring(1):h,y=m&&m>d?m-d:void 0;return{modifiers:s,hasImportantModifier:f,baseClassName:w,maybePostfixModifierPosition:y}};return n?o=>n({className:o,parseClassName:i}):i},FE=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(a=>{a[0]==="["?(t.push(...n.sort(),a),n=[]):n.push(a)}),t.push(...n.sort()),t},JE=e=>({cache:ZE(e.cacheSize),parseClassName:KE(e),...YE(e)}),$E=/\s+/,WE=(e,t)=>{const{parseClassName:n,getClassGroupId:a,getConflictingClassGroupIds:l}=t,r=[],i=e.trim().split($E);let o="";for(let s=i.length-1;s>=0;s-=1){const u=i[s],{modifiers:d,hasImportantModifier:m,baseClassName:h,maybePostfixModifierPosition:f}=n(u);let w=!!f,y=a(w?h.substring(0,f):h);if(!y){if(!w){o=u+(o.length>0?" "+o:o);continue}if(y=a(h),!y){o=u+(o.length>0?" "+o:o);continue}w=!1}const x=FE(d).join(":"),v=m?x+o0:x,g=v+y;if(r.includes(g))continue;r.push(g);const b=l(y,w);for(let S=0;S<b.length;++S){const E=b[S];r.push(v+E)}o=u+(o.length>0?" "+o:o)}return o};function IE(){let e=0,t,n,a="";for(;e<arguments.length;)(t=arguments[e++])&&(n=s0(t))&&(a&&(a+=" "),a+=n);return a}const s0=e=>{if(typeof e=="string")return e;let t,n="";for(let a=0;a<e.length;a++)e[a]&&(t=s0(e[a]))&&(n&&(n+=" "),n+=t);return n};function eA(e,...t){let n,a,l,r=i;function i(s){const u=t.reduce((d,m)=>m(d),e());return n=JE(u),a=n.cache.get,l=n.cache.set,r=o,o(s)}function o(s){const u=a(s);if(u)return u;const d=WE(s,n);return l(s,d),d}return function(){return r(IE.apply(null,arguments))}}const he=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},c0=/^\[(?:([a-z-]+):)?(.+)\]$/i,tA=/^\d+\/\d+$/,nA=new Set(["px","full","screen"]),aA=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,lA=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,rA=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,iA=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,oA=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,vn=e=>Yl(e)||nA.has(e)||tA.test(e),Pn=e=>wr(e,"length",pA),Yl=e=>!!e&&!Number.isNaN(Number(e)),Pc=e=>wr(e,"number",Yl),_r=e=>!!e&&Number.isInteger(Number(e)),sA=e=>e.endsWith("%")&&Yl(e.slice(0,-1)),Y=e=>c0.test(e),Zn=e=>aA.test(e),cA=new Set(["length","size","percentage"]),uA=e=>wr(e,cA,u0),fA=e=>wr(e,"position",u0),dA=new Set(["image","url"]),hA=e=>wr(e,dA,gA),mA=e=>wr(e,"",vA),zr=()=>!0,wr=(e,t,n)=>{const a=c0.exec(e);return a?a[1]?typeof t=="string"?a[1]===t:t.has(a[1]):n(a[2]):!1},pA=e=>lA.test(e)&&!rA.test(e),u0=()=>!1,vA=e=>iA.test(e),gA=e=>oA.test(e),yA=()=>{const e=he("colors"),t=he("spacing"),n=he("blur"),a=he("brightness"),l=he("borderColor"),r=he("borderRadius"),i=he("borderSpacing"),o=he("borderWidth"),s=he("contrast"),u=he("grayscale"),d=he("hueRotate"),m=he("invert"),h=he("gap"),f=he("gradientColorStops"),w=he("gradientColorStopPositions"),y=he("inset"),x=he("margin"),v=he("opacity"),g=he("padding"),b=he("saturate"),S=he("scale"),E=he("sepia"),T=he("skew"),A=he("space"),C=he("translate"),O=()=>["auto","contain","none"],M=()=>["auto","hidden","clip","visible","scroll"],B=()=>["auto",Y,t],L=()=>[Y,t],$=()=>["",vn,Pn],Z=()=>["auto",Yl,Y],ce=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],R=()=>["solid","dashed","dotted","double","none"],U=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],_=()=>["start","end","center","between","around","evenly","stretch"],z=()=>["","0",Y],k=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Ee=()=>[Yl,Y];return{cacheSize:500,separator:":",theme:{colors:[zr],spacing:[vn,Pn],blur:["none","",Zn,Y],brightness:Ee(),borderColor:[e],borderRadius:["none","","full",Zn,Y],borderSpacing:L(),borderWidth:$(),contrast:Ee(),grayscale:z(),hueRotate:Ee(),invert:z(),gap:L(),gradientColorStops:[e],gradientColorStopPositions:[sA,Pn],inset:B(),margin:B(),opacity:Ee(),padding:L(),saturate:Ee(),scale:Ee(),sepia:z(),skew:Ee(),space:L(),translate:L()},classGroups:{aspect:[{aspect:["auto","square","video",Y]}],container:["container"],columns:[{columns:[Zn]}],"break-after":[{"break-after":k()}],"break-before":[{"break-before":k()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...ce(),Y]}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:O()}],"overscroll-x":[{"overscroll-x":O()}],"overscroll-y":[{"overscroll-y":O()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",_r,Y]}],basis:[{basis:B()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",Y]}],grow:[{grow:z()}],shrink:[{shrink:z()}],order:[{order:["first","last","none",_r,Y]}],"grid-cols":[{"grid-cols":[zr]}],"col-start-end":[{col:["auto",{span:["full",_r,Y]},Y]}],"col-start":[{"col-start":Z()}],"col-end":[{"col-end":Z()}],"grid-rows":[{"grid-rows":[zr]}],"row-start-end":[{row:["auto",{span:[_r,Y]},Y]}],"row-start":[{"row-start":Z()}],"row-end":[{"row-end":Z()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",Y]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",Y]}],gap:[{gap:[h]}],"gap-x":[{"gap-x":[h]}],"gap-y":[{"gap-y":[h]}],"justify-content":[{justify:["normal",..._()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",..._(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[..._(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[g]}],px:[{px:[g]}],py:[{py:[g]}],ps:[{ps:[g]}],pe:[{pe:[g]}],pt:[{pt:[g]}],pr:[{pr:[g]}],pb:[{pb:[g]}],pl:[{pl:[g]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[A]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[A]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",Y,t]}],"min-w":[{"min-w":[Y,t,"min","max","fit"]}],"max-w":[{"max-w":[Y,t,"none","full","min","max","fit","prose",{screen:[Zn]},Zn]}],h:[{h:[Y,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[Y,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[Y,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[Y,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Zn,Pn]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Pc]}],"font-family":[{font:[zr]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",Y]}],"line-clamp":[{"line-clamp":["none",Yl,Pc]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",vn,Y]}],"list-image":[{"list-image":["none",Y]}],"list-style-type":[{list:["none","disc","decimal",Y]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...R(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",vn,Pn]}],"underline-offset":[{"underline-offset":["auto",vn,Y]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:L()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Y]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Y]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...ce(),fA]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",uA]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},hA]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[w]}],"gradient-via-pos":[{via:[w]}],"gradient-to-pos":[{to:[w]}],"gradient-from":[{from:[f]}],"gradient-via":[{via:[f]}],"gradient-to":[{to:[f]}],rounded:[{rounded:[r]}],"rounded-s":[{"rounded-s":[r]}],"rounded-e":[{"rounded-e":[r]}],"rounded-t":[{"rounded-t":[r]}],"rounded-r":[{"rounded-r":[r]}],"rounded-b":[{"rounded-b":[r]}],"rounded-l":[{"rounded-l":[r]}],"rounded-ss":[{"rounded-ss":[r]}],"rounded-se":[{"rounded-se":[r]}],"rounded-ee":[{"rounded-ee":[r]}],"rounded-es":[{"rounded-es":[r]}],"rounded-tl":[{"rounded-tl":[r]}],"rounded-tr":[{"rounded-tr":[r]}],"rounded-br":[{"rounded-br":[r]}],"rounded-bl":[{"rounded-bl":[r]}],"border-w":[{border:[o]}],"border-w-x":[{"border-x":[o]}],"border-w-y":[{"border-y":[o]}],"border-w-s":[{"border-s":[o]}],"border-w-e":[{"border-e":[o]}],"border-w-t":[{"border-t":[o]}],"border-w-r":[{"border-r":[o]}],"border-w-b":[{"border-b":[o]}],"border-w-l":[{"border-l":[o]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[...R(),"hidden"]}],"divide-x":[{"divide-x":[o]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[o]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:R()}],"border-color":[{border:[l]}],"border-color-x":[{"border-x":[l]}],"border-color-y":[{"border-y":[l]}],"border-color-s":[{"border-s":[l]}],"border-color-e":[{"border-e":[l]}],"border-color-t":[{"border-t":[l]}],"border-color-r":[{"border-r":[l]}],"border-color-b":[{"border-b":[l]}],"border-color-l":[{"border-l":[l]}],"divide-color":[{divide:[l]}],"outline-style":[{outline:["",...R()]}],"outline-offset":[{"outline-offset":[vn,Y]}],"outline-w":[{outline:[vn,Pn]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:$()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[vn,Pn]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Zn,mA]}],"shadow-color":[{shadow:[zr]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":[...U(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":U()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[a]}],contrast:[{contrast:[s]}],"drop-shadow":[{"drop-shadow":["","none",Zn,Y]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[m]}],saturate:[{saturate:[b]}],sepia:[{sepia:[E]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[a]}],"backdrop-contrast":[{"backdrop-contrast":[s]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[m]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[b]}],"backdrop-sepia":[{"backdrop-sepia":[E]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",Y]}],duration:[{duration:Ee()}],ease:[{ease:["linear","in","out","in-out",Y]}],delay:[{delay:Ee()}],animate:[{animate:["none","spin","ping","pulse","bounce",Y]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[S]}],"scale-x":[{"scale-x":[S]}],"scale-y":[{"scale-y":[S]}],rotate:[{rotate:[_r,Y]}],"translate-x":[{"translate-x":[C]}],"translate-y":[{"translate-y":[C]}],"skew-x":[{"skew-x":[T]}],"skew-y":[{"skew-y":[T]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",Y]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Y]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":L()}],"scroll-mx":[{"scroll-mx":L()}],"scroll-my":[{"scroll-my":L()}],"scroll-ms":[{"scroll-ms":L()}],"scroll-me":[{"scroll-me":L()}],"scroll-mt":[{"scroll-mt":L()}],"scroll-mr":[{"scroll-mr":L()}],"scroll-mb":[{"scroll-mb":L()}],"scroll-ml":[{"scroll-ml":L()}],"scroll-p":[{"scroll-p":L()}],"scroll-px":[{"scroll-px":L()}],"scroll-py":[{"scroll-py":L()}],"scroll-ps":[{"scroll-ps":L()}],"scroll-pe":[{"scroll-pe":L()}],"scroll-pt":[{"scroll-pt":L()}],"scroll-pr":[{"scroll-pr":L()}],"scroll-pb":[{"scroll-pb":L()}],"scroll-pl":[{"scroll-pl":L()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Y]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[vn,Pn,Pc]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},bA=eA(yA);function ae(...e){return bA(r0(e))}const xA=GE,SA=p.forwardRef(({className:e,sideOffset:t=4,...n},a)=>c.jsx(a0,{ref:a,sideOffset:t,className:ae("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));SA.displayName=a0.displayName;var Zs=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Ks=typeof window>"u"||"Deno"in globalThis;function Ht(){}function wA(e,t){return typeof e=="function"?e(t):e}function EA(e){return typeof e=="number"&&e>=0&&e!==1/0}function AA(e,t){return Math.max(e+(t||0)-Date.now(),0)}function of(e,t){return typeof e=="function"?e(t):e}function TA(e,t){return typeof e=="function"?e(t):e}function Bm(e,t){const{type:n="all",exact:a,fetchStatus:l,predicate:r,queryKey:i,stale:o}=e;if(i){if(a){if(t.queryHash!==Cd(i,t.options))return!1}else if(!Si(t.queryKey,i))return!1}if(n!=="all"){const s=t.isActive();if(n==="active"&&!s||n==="inactive"&&s)return!1}return!(typeof o=="boolean"&&t.isStale()!==o||l&&l!==t.state.fetchStatus||r&&!r(t))}function Hm(e,t){const{exact:n,status:a,predicate:l,mutationKey:r}=e;if(r){if(!t.options.mutationKey)return!1;if(n){if(xi(t.options.mutationKey)!==xi(r))return!1}else if(!Si(t.options.mutationKey,r))return!1}return!(a&&t.state.status!==a||l&&!l(t))}function Cd(e,t){return((t==null?void 0:t.queryKeyHashFn)||xi)(e)}function xi(e){return JSON.stringify(e,(t,n)=>sf(n)?Object.keys(n).sort().reduce((a,l)=>(a[l]=n[l],a),{}):n)}function Si(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?Object.keys(t).every(n=>Si(e[n],t[n])):!1}function f0(e,t){if(e===t)return e;const n=km(e)&&km(t);if(n||sf(e)&&sf(t)){const a=n?e:Object.keys(e),l=a.length,r=n?t:Object.keys(t),i=r.length,o=n?[]:{},s=new Set(a);let u=0;for(let d=0;d<i;d++){const m=n?d:r[d];(!n&&s.has(m)||n)&&e[m]===void 0&&t[m]===void 0?(o[m]=void 0,u++):(o[m]=f0(e[m],t[m]),o[m]===e[m]&&e[m]!==void 0&&u++)}return l===i&&u===l?e:o}return t}function km(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function sf(e){if(!qm(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!qm(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function qm(e){return Object.prototype.toString.call(e)==="[object Object]"}function NA(e){return new Promise(t=>{setTimeout(t,e)})}function CA(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?f0(e,t):t}function jA(e,t,n=0){const a=[...e,t];return n&&a.length>n?a.slice(1):a}function RA(e,t,n=0){const a=[t,...e];return n&&a.length>n?a.slice(0,-1):a}var jd=Symbol();function d0(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===jd?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var La,aa,Xl,vp,OA=(vp=class extends Zs{constructor(){super();W(this,La);W(this,aa);W(this,Xl);G(this,Xl,t=>{if(!Ks&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){N(this,aa)||this.setEventListener(N(this,Xl))}onUnsubscribe(){var t;this.hasListeners()||((t=N(this,aa))==null||t.call(this),G(this,aa,void 0))}setEventListener(t){var n;G(this,Xl,t),(n=N(this,aa))==null||n.call(this),G(this,aa,t(a=>{typeof a=="boolean"?this.setFocused(a):this.onFocus()}))}setFocused(t){N(this,La)!==t&&(G(this,La,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof N(this,La)=="boolean"?N(this,La):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},La=new WeakMap,aa=new WeakMap,Xl=new WeakMap,vp),h0=new OA,Pl,la,Zl,gp,MA=(gp=class extends Zs{constructor(){super();W(this,Pl,!0);W(this,la);W(this,Zl);G(this,Zl,t=>{if(!Ks&&window.addEventListener){const n=()=>t(!0),a=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",a,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",a)}}})}onSubscribe(){N(this,la)||this.setEventListener(N(this,Zl))}onUnsubscribe(){var t;this.hasListeners()||((t=N(this,la))==null||t.call(this),G(this,la,void 0))}setEventListener(t){var n;G(this,Zl,t),(n=N(this,la))==null||n.call(this),G(this,la,t(this.setOnline.bind(this)))}setOnline(t){N(this,Pl)!==t&&(G(this,Pl,t),this.listeners.forEach(a=>{a(t)}))}isOnline(){return N(this,Pl)}},Pl=new WeakMap,la=new WeakMap,Zl=new WeakMap,gp),vs=new MA;function DA(){let e,t;const n=new Promise((l,r)=>{e=l,t=r});n.status="pending",n.catch(()=>{});function a(l){Object.assign(n,l),delete n.resolve,delete n.reject}return n.resolve=l=>{a({status:"fulfilled",value:l}),e(l)},n.reject=l=>{a({status:"rejected",reason:l}),t(l)},n}function _A(e){return Math.min(1e3*2**e,3e4)}function m0(e){return(e??"online")==="online"?vs.isOnline():!0}var p0=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function Zc(e){return e instanceof p0}function v0(e){let t=!1,n=0,a=!1,l;const r=DA(),i=y=>{var x;a||(h(new p0(y)),(x=e.abort)==null||x.call(e))},o=()=>{t=!0},s=()=>{t=!1},u=()=>h0.isFocused()&&(e.networkMode==="always"||vs.isOnline())&&e.canRun(),d=()=>m0(e.networkMode)&&e.canRun(),m=y=>{var x;a||(a=!0,(x=e.onSuccess)==null||x.call(e,y),l==null||l(),r.resolve(y))},h=y=>{var x;a||(a=!0,(x=e.onError)==null||x.call(e,y),l==null||l(),r.reject(y))},f=()=>new Promise(y=>{var x;l=v=>{(a||u())&&y(v)},(x=e.onPause)==null||x.call(e)}).then(()=>{var y;l=void 0,a||(y=e.onContinue)==null||y.call(e)}),w=()=>{if(a)return;let y;const x=n===0?e.initialPromise:void 0;try{y=x??e.fn()}catch(v){y=Promise.reject(v)}Promise.resolve(y).then(m).catch(v=>{var T;if(a)return;const g=e.retry??(Ks?0:3),b=e.retryDelay??_A,S=typeof b=="function"?b(n,v):b,E=g===!0||typeof g=="number"&&n<g||typeof g=="function"&&g(n,v);if(t||!E){h(v);return}n++,(T=e.onFail)==null||T.call(e,n,v),NA(S).then(()=>u()?void 0:f()).then(()=>{t?h(v):w()})})};return{promise:r,cancel:i,continue:()=>(l==null||l(),r),cancelRetry:o,continueRetry:s,canStart:d,start:()=>(d()?w():f().then(w),r)}}var zA=e=>setTimeout(e,0);function UA(){let e=[],t=0,n=o=>{o()},a=o=>{o()},l=zA;const r=o=>{t?e.push(o):l(()=>{n(o)})},i=()=>{const o=e;e=[],o.length&&l(()=>{a(()=>{o.forEach(s=>{n(s)})})})};return{batch:o=>{let s;t++;try{s=o()}finally{t--,t||i()}return s},batchCalls:o=>(...s)=>{r(()=>{o(...s)})},schedule:r,setNotifyFunction:o=>{n=o},setBatchNotifyFunction:o=>{a=o},setScheduler:o=>{l=o}}}var We=UA(),Ba,yp,g0=(yp=class{constructor(){W(this,Ba)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),EA(this.gcTime)&&G(this,Ba,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(Ks?1/0:5*60*1e3))}clearGcTimeout(){N(this,Ba)&&(clearTimeout(N(this,Ba)),G(this,Ba,void 0))}},Ba=new WeakMap,yp),Kl,Ha,wt,ka,Ke,Ai,qa,kt,gn,bp,LA=(bp=class extends g0{constructor(t){super();W(this,kt);W(this,Kl);W(this,Ha);W(this,wt);W(this,ka);W(this,Ke);W(this,Ai);W(this,qa);G(this,qa,!1),G(this,Ai,t.defaultOptions),this.setOptions(t.options),this.observers=[],G(this,ka,t.client),G(this,wt,N(this,ka).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,G(this,Kl,HA(this.options)),this.state=t.state??N(this,Kl),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=N(this,Ke))==null?void 0:t.promise}setOptions(t){this.options={...N(this,Ai),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&N(this,wt).remove(this)}setData(t,n){const a=CA(this.state.data,t,this.options);return Ze(this,kt,gn).call(this,{data:a,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),a}setState(t,n){Ze(this,kt,gn).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var a,l;const n=(a=N(this,Ke))==null?void 0:a.promise;return(l=N(this,Ke))==null||l.cancel(t),n?n.then(Ht).catch(Ht):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(N(this,Kl))}isActive(){return this.observers.some(t=>TA(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===jd||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(t=>of(t.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(t=0){return this.state.data===void 0?!0:t==="static"?!1:this.state.isInvalidated?!0:!AA(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(a=>a.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=N(this,Ke))==null||n.continue()}onOnline(){var n;const t=this.observers.find(a=>a.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=N(this,Ke))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),N(this,wt).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(N(this,Ke)&&(N(this,qa)?N(this,Ke).cancel({revert:!0}):N(this,Ke).cancelRetry()),this.scheduleGc()),N(this,wt).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||Ze(this,kt,gn).call(this,{type:"invalidate"})}fetch(t,n){var u,d,m;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(N(this,Ke))return N(this,Ke).continueRetry(),N(this,Ke).promise}if(t&&this.setOptions(t),!this.options.queryFn){const h=this.observers.find(f=>f.options.queryFn);h&&this.setOptions(h.options)}const a=new AbortController,l=h=>{Object.defineProperty(h,"signal",{enumerable:!0,get:()=>(G(this,qa,!0),a.signal)})},r=()=>{const h=d0(this.options,n),w=(()=>{const y={client:N(this,ka),queryKey:this.queryKey,meta:this.meta};return l(y),y})();return G(this,qa,!1),this.options.persister?this.options.persister(h,w,this):h(w)},o=(()=>{const h={fetchOptions:n,options:this.options,queryKey:this.queryKey,client:N(this,ka),state:this.state,fetchFn:r};return l(h),h})();(u=this.options.behavior)==null||u.onFetch(o,this),G(this,Ha,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((d=o.fetchOptions)==null?void 0:d.meta))&&Ze(this,kt,gn).call(this,{type:"fetch",meta:(m=o.fetchOptions)==null?void 0:m.meta});const s=h=>{var f,w,y,x;Zc(h)&&h.silent||Ze(this,kt,gn).call(this,{type:"error",error:h}),Zc(h)||((w=(f=N(this,wt).config).onError)==null||w.call(f,h,this),(x=(y=N(this,wt).config).onSettled)==null||x.call(y,this.state.data,h,this)),this.scheduleGc()};return G(this,Ke,v0({initialPromise:n==null?void 0:n.initialPromise,fn:o.fetchFn,abort:a.abort.bind(a),onSuccess:h=>{var f,w,y,x;if(h===void 0){s(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(h)}catch(v){s(v);return}(w=(f=N(this,wt).config).onSuccess)==null||w.call(f,h,this),(x=(y=N(this,wt).config).onSettled)==null||x.call(y,h,this.state.error,this),this.scheduleGc()},onError:s,onFail:(h,f)=>{Ze(this,kt,gn).call(this,{type:"failed",failureCount:h,error:f})},onPause:()=>{Ze(this,kt,gn).call(this,{type:"pause"})},onContinue:()=>{Ze(this,kt,gn).call(this,{type:"continue"})},retry:o.options.retry,retryDelay:o.options.retryDelay,networkMode:o.options.networkMode,canRun:()=>!0})),N(this,Ke).start()}},Kl=new WeakMap,Ha=new WeakMap,wt=new WeakMap,ka=new WeakMap,Ke=new WeakMap,Ai=new WeakMap,qa=new WeakMap,kt=new WeakSet,gn=function(t){const n=a=>{switch(t.type){case"failed":return{...a,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...a,fetchStatus:"paused"};case"continue":return{...a,fetchStatus:"fetching"};case"fetch":return{...a,...BA(a.data,this.options),fetchMeta:t.meta??null};case"success":return G(this,Ha,void 0),{...a,data:t.data,dataUpdateCount:a.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const l=t.error;return Zc(l)&&l.revert&&N(this,Ha)?{...N(this,Ha),fetchStatus:"idle"}:{...a,error:l,errorUpdateCount:a.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:a.fetchFailureCount+1,fetchFailureReason:l,fetchStatus:"idle",status:"error"};case"invalidate":return{...a,isInvalidated:!0};case"setState":return{...a,...t.state}}};this.state=n(this.state),We.batch(()=>{this.observers.forEach(a=>{a.onQueryUpdate()}),N(this,wt).notify({query:this,type:"updated",action:t})})},bp);function BA(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:m0(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function HA(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,a=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?a??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var Kt,xp,kA=(xp=class extends Zs{constructor(t={}){super();W(this,Kt);this.config=t,G(this,Kt,new Map)}build(t,n,a){const l=n.queryKey,r=n.queryHash??Cd(l,n);let i=this.get(r);return i||(i=new LA({client:t,queryKey:l,queryHash:r,options:t.defaultQueryOptions(n),state:a,defaultOptions:t.getQueryDefaults(l)}),this.add(i)),i}add(t){N(this,Kt).has(t.queryHash)||(N(this,Kt).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=N(this,Kt).get(t.queryHash);n&&(t.destroy(),n===t&&N(this,Kt).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){We.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return N(this,Kt).get(t)}getAll(){return[...N(this,Kt).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(a=>Bm(n,a))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(a=>Bm(t,a)):n}notify(t){We.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){We.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){We.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},Kt=new WeakMap,xp),Ft,$e,Ga,Jt,Fn,Sp,qA=(Sp=class extends g0{constructor(t){super();W(this,Jt);W(this,Ft);W(this,$e);W(this,Ga);this.mutationId=t.mutationId,G(this,$e,t.mutationCache),G(this,Ft,[]),this.state=t.state||GA(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){N(this,Ft).includes(t)||(N(this,Ft).push(t),this.clearGcTimeout(),N(this,$e).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){G(this,Ft,N(this,Ft).filter(n=>n!==t)),this.scheduleGc(),N(this,$e).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){N(this,Ft).length||(this.state.status==="pending"?this.scheduleGc():N(this,$e).remove(this))}continue(){var t;return((t=N(this,Ga))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var r,i,o,s,u,d,m,h,f,w,y,x,v,g,b,S,E,T,A,C;const n=()=>{Ze(this,Jt,Fn).call(this,{type:"continue"})};G(this,Ga,v0({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(O,M)=>{Ze(this,Jt,Fn).call(this,{type:"failed",failureCount:O,error:M})},onPause:()=>{Ze(this,Jt,Fn).call(this,{type:"pause"})},onContinue:n,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>N(this,$e).canRun(this)}));const a=this.state.status==="pending",l=!N(this,Ga).canStart();try{if(a)n();else{Ze(this,Jt,Fn).call(this,{type:"pending",variables:t,isPaused:l}),await((i=(r=N(this,$e).config).onMutate)==null?void 0:i.call(r,t,this));const M=await((s=(o=this.options).onMutate)==null?void 0:s.call(o,t));M!==this.state.context&&Ze(this,Jt,Fn).call(this,{type:"pending",context:M,variables:t,isPaused:l})}const O=await N(this,Ga).start();return await((d=(u=N(this,$e).config).onSuccess)==null?void 0:d.call(u,O,t,this.state.context,this)),await((h=(m=this.options).onSuccess)==null?void 0:h.call(m,O,t,this.state.context)),await((w=(f=N(this,$e).config).onSettled)==null?void 0:w.call(f,O,null,this.state.variables,this.state.context,this)),await((x=(y=this.options).onSettled)==null?void 0:x.call(y,O,null,t,this.state.context)),Ze(this,Jt,Fn).call(this,{type:"success",data:O}),O}catch(O){try{throw await((g=(v=N(this,$e).config).onError)==null?void 0:g.call(v,O,t,this.state.context,this)),await((S=(b=this.options).onError)==null?void 0:S.call(b,O,t,this.state.context)),await((T=(E=N(this,$e).config).onSettled)==null?void 0:T.call(E,void 0,O,this.state.variables,this.state.context,this)),await((C=(A=this.options).onSettled)==null?void 0:C.call(A,void 0,O,t,this.state.context)),O}finally{Ze(this,Jt,Fn).call(this,{type:"error",error:O})}}finally{N(this,$e).runNext(this)}}},Ft=new WeakMap,$e=new WeakMap,Ga=new WeakMap,Jt=new WeakSet,Fn=function(t){const n=a=>{switch(t.type){case"failed":return{...a,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...a,isPaused:!0};case"continue":return{...a,isPaused:!1};case"pending":return{...a,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...a,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...a,data:void 0,error:t.error,failureCount:a.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),We.batch(()=>{N(this,Ft).forEach(a=>{a.onMutationUpdate(t)}),N(this,$e).notify({mutation:this,type:"updated",action:t})})},Sp);function GA(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var yn,qt,Ti,wp,YA=(wp=class extends Zs{constructor(t={}){super();W(this,yn);W(this,qt);W(this,Ti);this.config=t,G(this,yn,new Set),G(this,qt,new Map),G(this,Ti,0)}build(t,n,a){const l=new qA({mutationCache:this,mutationId:++Zi(this,Ti)._,options:t.defaultMutationOptions(n),state:a});return this.add(l),l}add(t){N(this,yn).add(t);const n=ho(t);if(typeof n=="string"){const a=N(this,qt).get(n);a?a.push(t):N(this,qt).set(n,[t])}this.notify({type:"added",mutation:t})}remove(t){if(N(this,yn).delete(t)){const n=ho(t);if(typeof n=="string"){const a=N(this,qt).get(n);if(a)if(a.length>1){const l=a.indexOf(t);l!==-1&&a.splice(l,1)}else a[0]===t&&N(this,qt).delete(n)}}this.notify({type:"removed",mutation:t})}canRun(t){const n=ho(t);if(typeof n=="string"){const a=N(this,qt).get(n),l=a==null?void 0:a.find(r=>r.state.status==="pending");return!l||l===t}else return!0}runNext(t){var a;const n=ho(t);if(typeof n=="string"){const l=(a=N(this,qt).get(n))==null?void 0:a.find(r=>r!==t&&r.state.isPaused);return(l==null?void 0:l.continue())??Promise.resolve()}else return Promise.resolve()}clear(){We.batch(()=>{N(this,yn).forEach(t=>{this.notify({type:"removed",mutation:t})}),N(this,yn).clear(),N(this,qt).clear()})}getAll(){return Array.from(N(this,yn))}find(t){const n={exact:!0,...t};return this.getAll().find(a=>Hm(n,a))}findAll(t={}){return this.getAll().filter(n=>Hm(t,n))}notify(t){We.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return We.batch(()=>Promise.all(t.map(n=>n.continue().catch(Ht))))}},yn=new WeakMap,qt=new WeakMap,Ti=new WeakMap,wp);function ho(e){var t;return(t=e.options.scope)==null?void 0:t.id}function Gm(e){return{onFetch:(t,n)=>{var d,m,h,f,w;const a=t.options,l=(h=(m=(d=t.fetchOptions)==null?void 0:d.meta)==null?void 0:m.fetchMore)==null?void 0:h.direction,r=((f=t.state.data)==null?void 0:f.pages)||[],i=((w=t.state.data)==null?void 0:w.pageParams)||[];let o={pages:[],pageParams:[]},s=0;const u=async()=>{let y=!1;const x=b=>{Object.defineProperty(b,"signal",{enumerable:!0,get:()=>(t.signal.aborted?y=!0:t.signal.addEventListener("abort",()=>{y=!0}),t.signal)})},v=d0(t.options,t.fetchOptions),g=async(b,S,E)=>{if(y)return Promise.reject();if(S==null&&b.pages.length)return Promise.resolve(b);const A=(()=>{const B={client:t.client,queryKey:t.queryKey,pageParam:S,direction:E?"backward":"forward",meta:t.options.meta};return x(B),B})(),C=await v(A),{maxPages:O}=t.options,M=E?RA:jA;return{pages:M(b.pages,C,O),pageParams:M(b.pageParams,S,O)}};if(l&&r.length){const b=l==="backward",S=b?VA:Ym,E={pages:r,pageParams:i},T=S(a,E);o=await g(E,T,b)}else{const b=e??r.length;do{const S=s===0?i[0]??a.initialPageParam:Ym(a,o);if(s>0&&S==null)break;o=await g(o,S),s++}while(s<b)}return o};t.options.persister?t.fetchFn=()=>{var y,x;return(x=(y=t.options).persister)==null?void 0:x.call(y,u,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=u}}}function Ym(e,{pages:t,pageParams:n}){const a=t.length-1;return t.length>0?e.getNextPageParam(t[a],t,n[a],n):void 0}function VA(e,{pages:t,pageParams:n}){var a;return t.length>0?(a=e.getPreviousPageParam)==null?void 0:a.call(e,t[0],t,n[0],n):void 0}var Ne,ra,ia,Fl,Jl,oa,$l,Wl,Ep,QA=(Ep=class{constructor(e={}){W(this,Ne);W(this,ra);W(this,ia);W(this,Fl);W(this,Jl);W(this,oa);W(this,$l);W(this,Wl);G(this,Ne,e.queryCache||new kA),G(this,ra,e.mutationCache||new YA),G(this,ia,e.defaultOptions||{}),G(this,Fl,new Map),G(this,Jl,new Map),G(this,oa,0)}mount(){Zi(this,oa)._++,N(this,oa)===1&&(G(this,$l,h0.subscribe(async e=>{e&&(await this.resumePausedMutations(),N(this,Ne).onFocus())})),G(this,Wl,vs.subscribe(async e=>{e&&(await this.resumePausedMutations(),N(this,Ne).onOnline())})))}unmount(){var e,t;Zi(this,oa)._--,N(this,oa)===0&&((e=N(this,$l))==null||e.call(this),G(this,$l,void 0),(t=N(this,Wl))==null||t.call(this),G(this,Wl,void 0))}isFetching(e){return N(this,Ne).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return N(this,ra).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=N(this,Ne).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),n=N(this,Ne).build(this,t),a=n.state.data;return a===void 0?this.fetchQuery(e):(e.revalidateIfStale&&n.isStaleByTime(of(t.staleTime,n))&&this.prefetchQuery(t),Promise.resolve(a))}getQueriesData(e){return N(this,Ne).findAll(e).map(({queryKey:t,state:n})=>{const a=n.data;return[t,a]})}setQueryData(e,t,n){const a=this.defaultQueryOptions({queryKey:e}),l=N(this,Ne).get(a.queryHash),r=l==null?void 0:l.state.data,i=wA(t,r);if(i!==void 0)return N(this,Ne).build(this,a).setData(i,{...n,manual:!0})}setQueriesData(e,t,n){return We.batch(()=>N(this,Ne).findAll(e).map(({queryKey:a})=>[a,this.setQueryData(a,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=N(this,Ne).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=N(this,Ne);We.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=N(this,Ne);return We.batch(()=>(n.findAll(e).forEach(a=>{a.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){const n={revert:!0,...t},a=We.batch(()=>N(this,Ne).findAll(e).map(l=>l.cancel(n)));return Promise.all(a).then(Ht).catch(Ht)}invalidateQueries(e,t={}){return We.batch(()=>(N(this,Ne).findAll(e).forEach(n=>{n.invalidate()}),(e==null?void 0:e.refetchType)==="none"?Promise.resolve():this.refetchQueries({...e,type:(e==null?void 0:e.refetchType)??(e==null?void 0:e.type)??"active"},t)))}refetchQueries(e,t={}){const n={...t,cancelRefetch:t.cancelRefetch??!0},a=We.batch(()=>N(this,Ne).findAll(e).filter(l=>!l.isDisabled()&&!l.isStatic()).map(l=>{let r=l.fetch(void 0,n);return n.throwOnError||(r=r.catch(Ht)),l.state.fetchStatus==="paused"?Promise.resolve():r}));return Promise.all(a).then(Ht)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=N(this,Ne).build(this,t);return n.isStaleByTime(of(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(Ht).catch(Ht)}fetchInfiniteQuery(e){return e.behavior=Gm(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(Ht).catch(Ht)}ensureInfiniteQueryData(e){return e.behavior=Gm(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return vs.isOnline()?N(this,ra).resumePausedMutations():Promise.resolve()}getQueryCache(){return N(this,Ne)}getMutationCache(){return N(this,ra)}getDefaultOptions(){return N(this,ia)}setDefaultOptions(e){G(this,ia,e)}setQueryDefaults(e,t){N(this,Fl).set(xi(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...N(this,Fl).values()],n={};return t.forEach(a=>{Si(e,a.queryKey)&&Object.assign(n,a.defaultOptions)}),n}setMutationDefaults(e,t){N(this,Jl).set(xi(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...N(this,Jl).values()],n={};return t.forEach(a=>{Si(e,a.mutationKey)&&Object.assign(n,a.defaultOptions)}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...N(this,ia).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=Cd(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===jd&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...N(this,ia).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){N(this,Ne).clear(),N(this,ra).clear()}},Ne=new WeakMap,ra=new WeakMap,ia=new WeakMap,Fl=new WeakMap,Jl=new WeakMap,oa=new WeakMap,$l=new WeakMap,Wl=new WeakMap,Ep),XA=p.createContext(void 0),PA=({client:e,children:t})=>(p.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),c.jsx(XA.Provider,{value:e,children:t}));/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function gs(){return gs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},gs.apply(this,arguments)}var da;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(da||(da={}));const Vm="popstate";function ZA(e){e===void 0&&(e={});function t(a,l){let{pathname:r,search:i,hash:o}=a.location;return cf("",{pathname:r,search:i,hash:o},l.state&&l.state.usr||null,l.state&&l.state.key||"default")}function n(a,l){return typeof l=="string"?l:b0(l)}return FA(t,n,null,e)}function st(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function y0(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function KA(){return Math.random().toString(36).substr(2,8)}function Qm(e,t){return{usr:e.state,key:e.key,idx:t}}function cf(e,t,n,a){return n===void 0&&(n=null),gs({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Fs(t):t,{state:n,key:t&&t.key||a||KA()})}function b0(e){let{pathname:t="/",search:n="",hash:a=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),a&&a!=="#"&&(t+=a.charAt(0)==="#"?a:"#"+a),t}function Fs(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let a=e.indexOf("?");a>=0&&(t.search=e.substr(a),e=e.substr(0,a)),e&&(t.pathname=e)}return t}function FA(e,t,n,a){a===void 0&&(a={});let{window:l=document.defaultView,v5Compat:r=!1}=a,i=l.history,o=da.Pop,s=null,u=d();u==null&&(u=0,i.replaceState(gs({},i.state,{idx:u}),""));function d(){return(i.state||{idx:null}).idx}function m(){o=da.Pop;let x=d(),v=x==null?null:x-u;u=x,s&&s({action:o,location:y.location,delta:v})}function h(x,v){o=da.Push;let g=cf(y.location,x,v);u=d()+1;let b=Qm(g,u),S=y.createHref(g);try{i.pushState(b,"",S)}catch(E){if(E instanceof DOMException&&E.name==="DataCloneError")throw E;l.location.assign(S)}r&&s&&s({action:o,location:y.location,delta:1})}function f(x,v){o=da.Replace;let g=cf(y.location,x,v);u=d();let b=Qm(g,u),S=y.createHref(g);i.replaceState(b,"",S),r&&s&&s({action:o,location:y.location,delta:0})}function w(x){let v=l.location.origin!=="null"?l.location.origin:l.location.href,g=typeof x=="string"?x:b0(x);return g=g.replace(/ $/,"%20"),st(v,"No window.location.(origin|href) available to create URL for href: "+g),new URL(g,v)}let y={get action(){return o},get location(){return e(l,i)},listen(x){if(s)throw new Error("A history only accepts one active listener");return l.addEventListener(Vm,m),s=x,()=>{l.removeEventListener(Vm,m),s=null}},createHref(x){return t(l,x)},createURL:w,encodeLocation(x){let v=w(x);return{pathname:v.pathname,search:v.search,hash:v.hash}},push:h,replace:f,go(x){return i.go(x)}};return y}var Xm;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Xm||(Xm={}));function JA(e,t,n){return n===void 0&&(n="/"),$A(e,t,n)}function $A(e,t,n,a){let l=typeof t=="string"?Fs(t):t,r=w0(l.pathname||"/",n);if(r==null)return null;let i=x0(e);WA(i);let o=null;for(let s=0;o==null&&s<i.length;++s){let u=uT(r);o=oT(i[s],u)}return o}function x0(e,t,n,a){t===void 0&&(t=[]),n===void 0&&(n=[]),a===void 0&&(a="");let l=(r,i,o)=>{let s={relativePath:o===void 0?r.path||"":o,caseSensitive:r.caseSensitive===!0,childrenIndex:i,route:r};s.relativePath.startsWith("/")&&(st(s.relativePath.startsWith(a),'Absolute route path "'+s.relativePath+'" nested under path '+('"'+a+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),s.relativePath=s.relativePath.slice(a.length));let u=Vl([a,s.relativePath]),d=n.concat(s);r.children&&r.children.length>0&&(st(r.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),x0(r.children,t,d,u)),!(r.path==null&&!r.index)&&t.push({path:u,score:rT(u,r.index),routesMeta:d})};return e.forEach((r,i)=>{var o;if(r.path===""||!((o=r.path)!=null&&o.includes("?")))l(r,i);else for(let s of S0(r.path))l(r,i,s)}),t}function S0(e){let t=e.split("/");if(t.length===0)return[];let[n,...a]=t,l=n.endsWith("?"),r=n.replace(/\?$/,"");if(a.length===0)return l?[r,""]:[r];let i=S0(a.join("/")),o=[];return o.push(...i.map(s=>s===""?r:[r,s].join("/"))),l&&o.push(...i),o.map(s=>e.startsWith("/")&&s===""?"/":s)}function WA(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:iT(t.routesMeta.map(a=>a.childrenIndex),n.routesMeta.map(a=>a.childrenIndex)))}const IA=/^:[\w-]+$/,eT=3,tT=2,nT=1,aT=10,lT=-2,Pm=e=>e==="*";function rT(e,t){let n=e.split("/"),a=n.length;return n.some(Pm)&&(a+=lT),t&&(a+=tT),n.filter(l=>!Pm(l)).reduce((l,r)=>l+(IA.test(r)?eT:r===""?nT:aT),a)}function iT(e,t){return e.length===t.length&&e.slice(0,-1).every((a,l)=>a===t[l])?e[e.length-1]-t[t.length-1]:0}function oT(e,t,n){let{routesMeta:a}=e,l={},r="/",i=[];for(let o=0;o<a.length;++o){let s=a[o],u=o===a.length-1,d=r==="/"?t:t.slice(r.length)||"/",m=sT({path:s.relativePath,caseSensitive:s.caseSensitive,end:u},d),h=s.route;if(!m)return null;Object.assign(l,m.params),i.push({params:l,pathname:Vl([r,m.pathname]),pathnameBase:fT(Vl([r,m.pathnameBase])),route:h}),m.pathnameBase!=="/"&&(r=Vl([r,m.pathnameBase]))}return i}function sT(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,a]=cT(e.path,e.caseSensitive,e.end),l=t.match(n);if(!l)return null;let r=l[0],i=r.replace(/(.)\/+$/,"$1"),o=l.slice(1);return{params:a.reduce((u,d,m)=>{let{paramName:h,isOptional:f}=d;if(h==="*"){let y=o[m]||"";i=r.slice(0,r.length-y.length).replace(/(.)\/+$/,"$1")}const w=o[m];return f&&!w?u[h]=void 0:u[h]=(w||"").replace(/%2F/g,"/"),u},{}),pathname:r,pathnameBase:i,pattern:e}}function cT(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),y0(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let a=[],l="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,o,s)=>(a.push({paramName:o,isOptional:s!=null}),s?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(a.push({paramName:"*"}),l+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?l+="\\/*$":e!==""&&e!=="/"&&(l+="(?:(?=\\/|$))"),[new RegExp(l,t?void 0:"i"),a]}function uT(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return y0(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function w0(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,a=e.charAt(n);return a&&a!=="/"?null:e.slice(n)||"/"}const Vl=e=>e.join("/").replace(/\/\/+/g,"/"),fT=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/");function dT(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const E0=["post","put","patch","delete"];new Set(E0);const hT=["get",...E0];new Set(hT);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ys(){return ys=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ys.apply(this,arguments)}const mT=p.createContext(null),pT=p.createContext(null),A0=p.createContext(null),Js=p.createContext(null),$s=p.createContext({outlet:null,matches:[],isDataRoute:!1}),T0=p.createContext(null);function Rd(){return p.useContext(Js)!=null}function vT(){return Rd()||st(!1),p.useContext(Js).location}function gT(e,t){return yT(e,t)}function yT(e,t,n,a){Rd()||st(!1);let{navigator:l}=p.useContext(A0),{matches:r}=p.useContext($s),i=r[r.length-1],o=i?i.params:{};i&&i.pathname;let s=i?i.pathnameBase:"/";i&&i.route;let u=vT(),d;if(t){var m;let x=typeof t=="string"?Fs(t):t;s==="/"||(m=x.pathname)!=null&&m.startsWith(s)||st(!1),d=x}else d=u;let h=d.pathname||"/",f=h;if(s!=="/"){let x=s.replace(/^\//,"").split("/");f="/"+h.replace(/^\//,"").split("/").slice(x.length).join("/")}let w=JA(e,{pathname:f}),y=ET(w&&w.map(x=>Object.assign({},x,{params:Object.assign({},o,x.params),pathname:Vl([s,l.encodeLocation?l.encodeLocation(x.pathname).pathname:x.pathname]),pathnameBase:x.pathnameBase==="/"?s:Vl([s,l.encodeLocation?l.encodeLocation(x.pathnameBase).pathname:x.pathnameBase])})),r,n,a);return t&&y?p.createElement(Js.Provider,{value:{location:ys({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:da.Pop}},y):y}function bT(){let e=CT(),t=dT(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,l={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return p.createElement(p.Fragment,null,p.createElement("h2",null,"Unexpected Application Error!"),p.createElement("h3",{style:{fontStyle:"italic"}},t),n?p.createElement("pre",{style:l},n):null,null)}const xT=p.createElement(bT,null);class ST extends p.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?p.createElement($s.Provider,{value:this.props.routeContext},p.createElement(T0.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function wT(e){let{routeContext:t,match:n,children:a}=e,l=p.useContext(mT);return l&&l.static&&l.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(l.staticContext._deepestRenderedBoundaryId=n.route.id),p.createElement($s.Provider,{value:t},a)}function ET(e,t,n,a){var l;if(t===void 0&&(t=[]),n===void 0&&(n=null),a===void 0&&(a=null),e==null){var r;if(!n)return null;if(n.errors)e=n.matches;else if((r=a)!=null&&r.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,o=(l=n)==null?void 0:l.errors;if(o!=null){let d=i.findIndex(m=>m.route.id&&(o==null?void 0:o[m.route.id])!==void 0);d>=0||st(!1),i=i.slice(0,Math.min(i.length,d+1))}let s=!1,u=-1;if(n&&a&&a.v7_partialHydration)for(let d=0;d<i.length;d++){let m=i[d];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(u=d),m.route.id){let{loaderData:h,errors:f}=n,w=m.route.loader&&h[m.route.id]===void 0&&(!f||f[m.route.id]===void 0);if(m.route.lazy||w){s=!0,u>=0?i=i.slice(0,u+1):i=[i[0]];break}}}return i.reduceRight((d,m,h)=>{let f,w=!1,y=null,x=null;n&&(f=o&&m.route.id?o[m.route.id]:void 0,y=m.route.errorElement||xT,s&&(u<0&&h===0?(jT("route-fallback"),w=!0,x=null):u===h&&(w=!0,x=m.route.hydrateFallbackElement||null)));let v=t.concat(i.slice(0,h+1)),g=()=>{let b;return f?b=y:w?b=x:m.route.Component?b=p.createElement(m.route.Component,null):m.route.element?b=m.route.element:b=d,p.createElement(wT,{match:m,routeContext:{outlet:d,matches:v,isDataRoute:n!=null},children:b})};return n&&(m.route.ErrorBoundary||m.route.errorElement||h===0)?p.createElement(ST,{location:n.location,revalidation:n.revalidation,component:y,error:f,children:g(),routeContext:{outlet:null,matches:v,isDataRoute:!0}}):g()},null)}var N0=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(N0||{});function AT(e){let t=p.useContext(pT);return t||st(!1),t}function TT(e){let t=p.useContext($s);return t||st(!1),t}function NT(e){let t=TT(),n=t.matches[t.matches.length-1];return n.route.id||st(!1),n.route.id}function CT(){var e;let t=p.useContext(T0),n=AT(N0.UseRouteError),a=NT();return t!==void 0?t:(e=n.errors)==null?void 0:e[a]}const Zm={};function jT(e,t,n){Zm[e]||(Zm[e]=!0)}function RT(e,t){e==null||e.v7_startTransition,e==null||e.v7_relativeSplatPath}function uf(e){st(!1)}function OT(e){let{basename:t="/",children:n=null,location:a,navigationType:l=da.Pop,navigator:r,static:i=!1,future:o}=e;Rd()&&st(!1);let s=t.replace(/^\/*/,"/"),u=p.useMemo(()=>({basename:s,navigator:r,static:i,future:ys({v7_relativeSplatPath:!1},o)}),[s,o,r,i]);typeof a=="string"&&(a=Fs(a));let{pathname:d="/",search:m="",hash:h="",state:f=null,key:w="default"}=a,y=p.useMemo(()=>{let x=w0(d,s);return x==null?null:{location:{pathname:x,search:m,hash:h,state:f,key:w},navigationType:l}},[s,d,m,h,f,w,l]);return y==null?null:p.createElement(A0.Provider,{value:u},p.createElement(Js.Provider,{children:n,value:y}))}function MT(e){let{children:t,location:n}=e;return gT(ff(t),n)}new Promise(()=>{});function ff(e,t){t===void 0&&(t=[]);let n=[];return p.Children.forEach(e,(a,l)=>{if(!p.isValidElement(a))return;let r=[...t,l];if(a.type===p.Fragment){n.push.apply(n,ff(a.props.children,r));return}a.type!==uf&&st(!1),!a.props.index||!a.props.children||st(!1);let i={id:a.props.id||r.join("-"),caseSensitive:a.props.caseSensitive,element:a.props.element,Component:a.props.Component,index:a.props.index,path:a.props.path,loader:a.props.loader,action:a.props.action,errorElement:a.props.errorElement,ErrorBoundary:a.props.ErrorBoundary,hasErrorBoundary:a.props.ErrorBoundary!=null||a.props.errorElement!=null,shouldRevalidate:a.props.shouldRevalidate,handle:a.props.handle,lazy:a.props.lazy};a.props.children&&(i.children=ff(a.props.children,r)),n.push(i)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const DT="6";try{window.__reactRouterVersion=DT}catch{}const _T="startTransition",Km=xf[_T];function zT(e){let{basename:t,children:n,future:a,window:l}=e,r=p.useRef();r.current==null&&(r.current=ZA({window:l,v5Compat:!0}));let i=r.current,[o,s]=p.useState({action:i.action,location:i.location}),{v7_startTransition:u}=a||{},d=p.useCallback(m=>{u&&Km?Km(()=>s(m)):s(m)},[s,u]);return p.useLayoutEffect(()=>i.listen(d),[i,d]),p.useEffect(()=>RT(a),[a]),p.createElement(OT,{basename:t,children:n,location:o.location,navigationType:o.action,navigator:i,future:a})}var Fm;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Fm||(Fm={}));var Jm;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Jm||(Jm={}));const $m=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Wm=r0,Ws=(e,t)=>n=>{var a;if((t==null?void 0:t.variants)==null)return Wm(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:l,defaultVariants:r}=t,i=Object.keys(l).map(u=>{const d=n==null?void 0:n[u],m=r==null?void 0:r[u];if(d===null)return null;const h=$m(d)||$m(m);return l[u][h]}),o=n&&Object.entries(n).reduce((u,d)=>{let[m,h]=d;return h===void 0||(u[m]=h),u},{}),s=t==null||(a=t.compoundVariants)===null||a===void 0?void 0:a.reduce((u,d)=>{let{class:m,className:h,...f}=d;return Object.entries(f).every(w=>{let[y,x]=w;return Array.isArray(x)?x.includes({...r,...o}[y]):{...r,...o}[y]===x})?[...u,m,h]:u},[]);return Wm(e,i,s,n==null?void 0:n.class,n==null?void 0:n.className)},UT=Ws("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),Wt=p.forwardRef(({className:e,variant:t,size:n,asChild:a=!1,...l},r)=>{const i=a?Ow:"button";return c.jsx(i,{className:ae(UT({variant:t,size:n,className:e})),ref:r,...l})});Wt.displayName="Button";const C0=p.forwardRef(({className:e,type:t,...n},a)=>c.jsx("input",{type:t,className:ae("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...n}));C0.displayName="Input";var LT=p.createContext(void 0);function Od(e){const t=p.useContext(LT);return e||t||"ltr"}function BT(e,[t,n]){return Math.min(n,Math.max(t,e))}function HT(e,t){return p.useReducer((n,a)=>t[n][a]??n,e)}var Md="ScrollArea",[j0,mj]=rl(Md),[kT,_t]=j0(Md),R0=p.forwardRef((e,t)=>{const{__scopeScrollArea:n,type:a="hover",dir:l,scrollHideDelay:r=600,...i}=e,[o,s]=p.useState(null),[u,d]=p.useState(null),[m,h]=p.useState(null),[f,w]=p.useState(null),[y,x]=p.useState(null),[v,g]=p.useState(0),[b,S]=p.useState(0),[E,T]=p.useState(!1),[A,C]=p.useState(!1),O=Le(t,B=>s(B)),M=Od(l);return c.jsx(kT,{scope:n,type:a,dir:M,scrollHideDelay:r,scrollArea:o,viewport:u,onViewportChange:d,content:m,onContentChange:h,scrollbarX:f,onScrollbarXChange:w,scrollbarXEnabled:E,onScrollbarXEnabledChange:T,scrollbarY:y,onScrollbarYChange:x,scrollbarYEnabled:A,onScrollbarYEnabledChange:C,onCornerWidthChange:g,onCornerHeightChange:S,children:c.jsx(pe.div,{dir:M,...i,ref:O,style:{position:"relative","--radix-scroll-area-corner-width":v+"px","--radix-scroll-area-corner-height":b+"px",...e.style}})})});R0.displayName=Md;var O0="ScrollAreaViewport",M0=p.forwardRef((e,t)=>{const{__scopeScrollArea:n,children:a,nonce:l,...r}=e,i=_t(O0,n),o=p.useRef(null),s=Le(t,o,i.onViewportChange);return c.jsxs(c.Fragment,{children:[c.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),c.jsx(pe.div,{"data-radix-scroll-area-viewport":"",...r,ref:s,style:{overflowX:i.scrollbarXEnabled?"scroll":"hidden",overflowY:i.scrollbarYEnabled?"scroll":"hidden",...e.style},children:c.jsx("div",{ref:i.onContentChange,style:{minWidth:"100%",display:"table"},children:a})})]})});M0.displayName=O0;var dn="ScrollAreaScrollbar",Dd=p.forwardRef((e,t)=>{const{forceMount:n,...a}=e,l=_t(dn,e.__scopeScrollArea),{onScrollbarXEnabledChange:r,onScrollbarYEnabledChange:i}=l,o=e.orientation==="horizontal";return p.useEffect(()=>(o?r(!0):i(!0),()=>{o?r(!1):i(!1)}),[o,r,i]),l.type==="hover"?c.jsx(qT,{...a,ref:t,forceMount:n}):l.type==="scroll"?c.jsx(GT,{...a,ref:t,forceMount:n}):l.type==="auto"?c.jsx(D0,{...a,ref:t,forceMount:n}):l.type==="always"?c.jsx(_d,{...a,ref:t}):null});Dd.displayName=dn;var qT=p.forwardRef((e,t)=>{const{forceMount:n,...a}=e,l=_t(dn,e.__scopeScrollArea),[r,i]=p.useState(!1);return p.useEffect(()=>{const o=l.scrollArea;let s=0;if(o){const u=()=>{window.clearTimeout(s),i(!0)},d=()=>{s=window.setTimeout(()=>i(!1),l.scrollHideDelay)};return o.addEventListener("pointerenter",u),o.addEventListener("pointerleave",d),()=>{window.clearTimeout(s),o.removeEventListener("pointerenter",u),o.removeEventListener("pointerleave",d)}}},[l.scrollArea,l.scrollHideDelay]),c.jsx(fn,{present:n||r,children:c.jsx(D0,{"data-state":r?"visible":"hidden",...a,ref:t})})}),GT=p.forwardRef((e,t)=>{const{forceMount:n,...a}=e,l=_t(dn,e.__scopeScrollArea),r=e.orientation==="horizontal",i=ec(()=>s("SCROLL_END"),100),[o,s]=HT("hidden",{hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}});return p.useEffect(()=>{if(o==="idle"){const u=window.setTimeout(()=>s("HIDE"),l.scrollHideDelay);return()=>window.clearTimeout(u)}},[o,l.scrollHideDelay,s]),p.useEffect(()=>{const u=l.viewport,d=r?"scrollLeft":"scrollTop";if(u){let m=u[d];const h=()=>{const f=u[d];m!==f&&(s("SCROLL"),i()),m=f};return u.addEventListener("scroll",h),()=>u.removeEventListener("scroll",h)}},[l.viewport,r,s,i]),c.jsx(fn,{present:n||o!=="hidden",children:c.jsx(_d,{"data-state":o==="hidden"?"hidden":"visible",...a,ref:t,onPointerEnter:ne(e.onPointerEnter,()=>s("POINTER_ENTER")),onPointerLeave:ne(e.onPointerLeave,()=>s("POINTER_LEAVE"))})})}),D0=p.forwardRef((e,t)=>{const n=_t(dn,e.__scopeScrollArea),{forceMount:a,...l}=e,[r,i]=p.useState(!1),o=e.orientation==="horizontal",s=ec(()=>{if(n.viewport){const u=n.viewport.offsetWidth<n.viewport.scrollWidth,d=n.viewport.offsetHeight<n.viewport.scrollHeight;i(o?u:d)}},10);return dr(n.viewport,s),dr(n.content,s),c.jsx(fn,{present:a||r,children:c.jsx(_d,{"data-state":r?"visible":"hidden",...l,ref:t})})}),_d=p.forwardRef((e,t)=>{const{orientation:n="vertical",...a}=e,l=_t(dn,e.__scopeScrollArea),r=p.useRef(null),i=p.useRef(0),[o,s]=p.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=B0(o.viewport,o.content),d={...a,sizes:o,onSizesChange:s,hasThumb:u>0&&u<1,onThumbChange:h=>r.current=h,onThumbPointerUp:()=>i.current=0,onThumbPointerDown:h=>i.current=h};function m(h,f){return ZT(h,i.current,o,f)}return n==="horizontal"?c.jsx(YT,{...d,ref:t,onThumbPositionChange:()=>{if(l.viewport&&r.current){const h=l.viewport.scrollLeft,f=Im(h,o,l.dir);r.current.style.transform=`translate3d(${f}px, 0, 0)`}},onWheelScroll:h=>{l.viewport&&(l.viewport.scrollLeft=h)},onDragScroll:h=>{l.viewport&&(l.viewport.scrollLeft=m(h,l.dir))}}):n==="vertical"?c.jsx(VT,{...d,ref:t,onThumbPositionChange:()=>{if(l.viewport&&r.current){const h=l.viewport.scrollTop,f=Im(h,o);r.current.style.transform=`translate3d(0, ${f}px, 0)`}},onWheelScroll:h=>{l.viewport&&(l.viewport.scrollTop=h)},onDragScroll:h=>{l.viewport&&(l.viewport.scrollTop=m(h))}}):null}),YT=p.forwardRef((e,t)=>{const{sizes:n,onSizesChange:a,...l}=e,r=_t(dn,e.__scopeScrollArea),[i,o]=p.useState(),s=p.useRef(null),u=Le(t,s,r.onScrollbarXChange);return p.useEffect(()=>{s.current&&o(getComputedStyle(s.current))},[s]),c.jsx(z0,{"data-orientation":"horizontal",...l,ref:u,sizes:n,style:{bottom:0,left:r.dir==="rtl"?"var(--radix-scroll-area-corner-width)":0,right:r.dir==="ltr"?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":Is(n)+"px",...e.style},onThumbPointerDown:d=>e.onThumbPointerDown(d.x),onDragScroll:d=>e.onDragScroll(d.x),onWheelScroll:(d,m)=>{if(r.viewport){const h=r.viewport.scrollLeft+d.deltaX;e.onWheelScroll(h),k0(h,m)&&d.preventDefault()}},onResize:()=>{s.current&&r.viewport&&i&&a({content:r.viewport.scrollWidth,viewport:r.viewport.offsetWidth,scrollbar:{size:s.current.clientWidth,paddingStart:xs(i.paddingLeft),paddingEnd:xs(i.paddingRight)}})}})}),VT=p.forwardRef((e,t)=>{const{sizes:n,onSizesChange:a,...l}=e,r=_t(dn,e.__scopeScrollArea),[i,o]=p.useState(),s=p.useRef(null),u=Le(t,s,r.onScrollbarYChange);return p.useEffect(()=>{s.current&&o(getComputedStyle(s.current))},[s]),c.jsx(z0,{"data-orientation":"vertical",...l,ref:u,sizes:n,style:{top:0,right:r.dir==="ltr"?0:void 0,left:r.dir==="rtl"?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":Is(n)+"px",...e.style},onThumbPointerDown:d=>e.onThumbPointerDown(d.y),onDragScroll:d=>e.onDragScroll(d.y),onWheelScroll:(d,m)=>{if(r.viewport){const h=r.viewport.scrollTop+d.deltaY;e.onWheelScroll(h),k0(h,m)&&d.preventDefault()}},onResize:()=>{s.current&&r.viewport&&i&&a({content:r.viewport.scrollHeight,viewport:r.viewport.offsetHeight,scrollbar:{size:s.current.clientHeight,paddingStart:xs(i.paddingTop),paddingEnd:xs(i.paddingBottom)}})}})}),[QT,_0]=j0(dn),z0=p.forwardRef((e,t)=>{const{__scopeScrollArea:n,sizes:a,hasThumb:l,onThumbChange:r,onThumbPointerUp:i,onThumbPointerDown:o,onThumbPositionChange:s,onDragScroll:u,onWheelScroll:d,onResize:m,...h}=e,f=_t(dn,n),[w,y]=p.useState(null),x=Le(t,O=>y(O)),v=p.useRef(null),g=p.useRef(""),b=f.viewport,S=a.content-a.viewport,E=it(d),T=it(s),A=ec(m,10);function C(O){if(v.current){const M=O.clientX-v.current.left,B=O.clientY-v.current.top;u({x:M,y:B})}}return p.useEffect(()=>{const O=M=>{const B=M.target;(w==null?void 0:w.contains(B))&&E(M,S)};return document.addEventListener("wheel",O,{passive:!1}),()=>document.removeEventListener("wheel",O,{passive:!1})},[b,w,S,E]),p.useEffect(T,[a,T]),dr(w,A),dr(f.content,A),c.jsx(QT,{scope:n,scrollbar:w,hasThumb:l,onThumbChange:it(r),onThumbPointerUp:it(i),onThumbPositionChange:T,onThumbPointerDown:it(o),children:c.jsx(pe.div,{...h,ref:x,style:{position:"absolute",...h.style},onPointerDown:ne(e.onPointerDown,O=>{O.button===0&&(O.target.setPointerCapture(O.pointerId),v.current=w.getBoundingClientRect(),g.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",f.viewport&&(f.viewport.style.scrollBehavior="auto"),C(O))}),onPointerMove:ne(e.onPointerMove,C),onPointerUp:ne(e.onPointerUp,O=>{const M=O.target;M.hasPointerCapture(O.pointerId)&&M.releasePointerCapture(O.pointerId),document.body.style.webkitUserSelect=g.current,f.viewport&&(f.viewport.style.scrollBehavior=""),v.current=null})})})}),bs="ScrollAreaThumb",U0=p.forwardRef((e,t)=>{const{forceMount:n,...a}=e,l=_0(bs,e.__scopeScrollArea);return c.jsx(fn,{present:n||l.hasThumb,children:c.jsx(XT,{ref:t,...a})})}),XT=p.forwardRef((e,t)=>{const{__scopeScrollArea:n,style:a,...l}=e,r=_t(bs,n),i=_0(bs,n),{onThumbPositionChange:o}=i,s=Le(t,m=>i.onThumbChange(m)),u=p.useRef(void 0),d=ec(()=>{u.current&&(u.current(),u.current=void 0)},100);return p.useEffect(()=>{const m=r.viewport;if(m){const h=()=>{if(d(),!u.current){const f=KT(m,o);u.current=f,o()}};return o(),m.addEventListener("scroll",h),()=>m.removeEventListener("scroll",h)}},[r.viewport,d,o]),c.jsx(pe.div,{"data-state":i.hasThumb?"visible":"hidden",...l,ref:s,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...a},onPointerDownCapture:ne(e.onPointerDownCapture,m=>{const f=m.target.getBoundingClientRect(),w=m.clientX-f.left,y=m.clientY-f.top;i.onThumbPointerDown({x:w,y})}),onPointerUp:ne(e.onPointerUp,i.onThumbPointerUp)})});U0.displayName=bs;var zd="ScrollAreaCorner",L0=p.forwardRef((e,t)=>{const n=_t(zd,e.__scopeScrollArea),a=!!(n.scrollbarX&&n.scrollbarY);return n.type!=="scroll"&&a?c.jsx(PT,{...e,ref:t}):null});L0.displayName=zd;var PT=p.forwardRef((e,t)=>{const{__scopeScrollArea:n,...a}=e,l=_t(zd,n),[r,i]=p.useState(0),[o,s]=p.useState(0),u=!!(r&&o);return dr(l.scrollbarX,()=>{var m;const d=((m=l.scrollbarX)==null?void 0:m.offsetHeight)||0;l.onCornerHeightChange(d),s(d)}),dr(l.scrollbarY,()=>{var m;const d=((m=l.scrollbarY)==null?void 0:m.offsetWidth)||0;l.onCornerWidthChange(d),i(d)}),u?c.jsx(pe.div,{...a,ref:t,style:{width:r,height:o,position:"absolute",right:l.dir==="ltr"?0:void 0,left:l.dir==="rtl"?0:void 0,bottom:0,...e.style}}):null});function xs(e){return e?parseInt(e,10):0}function B0(e,t){const n=e/t;return isNaN(n)?0:n}function Is(e){const t=B0(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,a=(e.scrollbar.size-n)*t;return Math.max(a,18)}function ZT(e,t,n,a="ltr"){const l=Is(n),r=l/2,i=t||r,o=l-i,s=n.scrollbar.paddingStart+i,u=n.scrollbar.size-n.scrollbar.paddingEnd-o,d=n.content-n.viewport,m=a==="ltr"?[0,d]:[d*-1,0];return H0([s,u],m)(e)}function Im(e,t,n="ltr"){const a=Is(t),l=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,r=t.scrollbar.size-l,i=t.content-t.viewport,o=r-a,s=n==="ltr"?[0,i]:[i*-1,0],u=BT(e,s);return H0([0,i],[0,o])(u)}function H0(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const a=(t[1]-t[0])/(e[1]-e[0]);return t[0]+a*(n-e[0])}}function k0(e,t){return e>0&&e<t}var KT=(e,t=()=>{})=>{let n={left:e.scrollLeft,top:e.scrollTop},a=0;return function l(){const r={left:e.scrollLeft,top:e.scrollTop},i=n.left!==r.left,o=n.top!==r.top;(i||o)&&t(),n=r,a=window.requestAnimationFrame(l)}(),()=>window.cancelAnimationFrame(a)};function ec(e,t){const n=it(e),a=p.useRef(0);return p.useEffect(()=>()=>window.clearTimeout(a.current),[]),p.useCallback(()=>{window.clearTimeout(a.current),a.current=window.setTimeout(n,t)},[n,t])}function dr(e,t){const n=it(t);_n(()=>{let a=0;if(e){const l=new ResizeObserver(()=>{cancelAnimationFrame(a),a=window.requestAnimationFrame(n)});return l.observe(e),()=>{window.cancelAnimationFrame(a),l.unobserve(e)}}},[e,n])}var q0=R0,FT=M0,JT=L0;const Ud=p.forwardRef(({className:e,children:t,...n},a)=>c.jsxs(q0,{ref:a,className:ae("relative overflow-hidden",e),...n,children:[c.jsx(FT,{className:"h-full w-full rounded-[inherit]",children:t}),c.jsx(G0,{}),c.jsx(JT,{})]}));Ud.displayName=q0.displayName;const G0=p.forwardRef(({className:e,orientation:t="vertical",...n},a)=>c.jsx(Dd,{ref:a,orientation:t,className:ae("flex touch-none select-none transition-colors",t==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-[1px]",t==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...n,children:c.jsx(U0,{className:"relative flex-1 rounded-full bg-border"})}));G0.displayName=Dd.displayName;/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $T=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Y0=(...e)=>e.filter((t,n,a)=>!!t&&t.trim()!==""&&a.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var WT={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const IT=p.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:a,className:l="",children:r,iconNode:i,...o},s)=>p.createElement("svg",{ref:s,...WT,width:t,height:t,stroke:e,strokeWidth:a?Number(n)*24/Number(t):n,className:Y0("lucide",l),...o},[...i.map(([u,d])=>p.createElement(u,d)),...Array.isArray(r)?r:[r]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Te=(e,t)=>{const n=p.forwardRef(({className:a,...l},r)=>p.createElement(IT,{ref:r,iconNode:t,className:Y0(`lucide-${$T(e)}`,a),...l}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ep=Te("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eN=Te("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tp=Te("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tN=Te("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nN=Te("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aN=Te("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kc=Te("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fc=Te("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wi=Te("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const np=Te("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lN=Te("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rN=Te("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iN=Te("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ei=Te("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ap=Te("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lp=Te("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ss=Te("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oN=Te("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sN=Te("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rp=Te("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cN=Te("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uN=Te("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fN=Te("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]);var Jc="focusScope.autoFocusOnMount",$c="focusScope.autoFocusOnUnmount",ip={bubbles:!1,cancelable:!0},dN="FocusScope",V0=p.forwardRef((e,t)=>{const{loop:n=!1,trapped:a=!1,onMountAutoFocus:l,onUnmountAutoFocus:r,...i}=e,[o,s]=p.useState(null),u=it(l),d=it(r),m=p.useRef(null),h=Le(t,y=>s(y)),f=p.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;p.useEffect(()=>{if(a){let y=function(b){if(f.paused||!o)return;const S=b.target;o.contains(S)?m.current=S:Jn(m.current,{select:!0})},x=function(b){if(f.paused||!o)return;const S=b.relatedTarget;S!==null&&(o.contains(S)||Jn(m.current,{select:!0}))},v=function(b){if(document.activeElement===document.body)for(const E of b)E.removedNodes.length>0&&Jn(o)};document.addEventListener("focusin",y),document.addEventListener("focusout",x);const g=new MutationObserver(v);return o&&g.observe(o,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",y),document.removeEventListener("focusout",x),g.disconnect()}}},[a,o,f.paused]),p.useEffect(()=>{if(o){sp.add(f);const y=document.activeElement;if(!o.contains(y)){const v=new CustomEvent(Jc,ip);o.addEventListener(Jc,u),o.dispatchEvent(v),v.defaultPrevented||(hN(yN(Q0(o)),{select:!0}),document.activeElement===y&&Jn(o))}return()=>{o.removeEventListener(Jc,u),setTimeout(()=>{const v=new CustomEvent($c,ip);o.addEventListener($c,d),o.dispatchEvent(v),v.defaultPrevented||Jn(y??document.body,{select:!0}),o.removeEventListener($c,d),sp.remove(f)},0)}}},[o,u,d,f]);const w=p.useCallback(y=>{if(!n&&!a||f.paused)return;const x=y.key==="Tab"&&!y.altKey&&!y.ctrlKey&&!y.metaKey,v=document.activeElement;if(x&&v){const g=y.currentTarget,[b,S]=mN(g);b&&S?!y.shiftKey&&v===S?(y.preventDefault(),n&&Jn(b,{select:!0})):y.shiftKey&&v===b&&(y.preventDefault(),n&&Jn(S,{select:!0})):v===g&&y.preventDefault()}},[n,a,f.paused]);return c.jsx(pe.div,{tabIndex:-1,...i,ref:h,onKeyDown:w})});V0.displayName=dN;function hN(e,{select:t=!1}={}){const n=document.activeElement;for(const a of e)if(Jn(a,{select:t}),document.activeElement!==n)return}function mN(e){const t=Q0(e),n=op(t,e),a=op(t.reverse(),e);return[n,a]}function Q0(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{const l=a.tagName==="INPUT"&&a.type==="hidden";return a.disabled||a.hidden||l?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function op(e,t){for(const n of e)if(!pN(n,{upTo:t}))return n}function pN(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function vN(e){return e instanceof HTMLInputElement&&"select"in e}function Jn(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&vN(e)&&t&&e.select()}}var sp=gN();function gN(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=cp(e,t),e.unshift(t)},remove(t){var n;e=cp(e,t),(n=e[0])==null||n.resume()}}}function cp(e,t){const n=[...e],a=n.indexOf(t);return a!==-1&&n.splice(a,1),n}function yN(e){return e.filter(t=>t.tagName!=="A")}var Wc=0;function bN(){p.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??up()),document.body.insertAdjacentElement("beforeend",e[1]??up()),Wc++,()=>{Wc===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Wc--}},[])}function up(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var It=function(){return It=Object.assign||function(t){for(var n,a=1,l=arguments.length;a<l;a++){n=arguments[a];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},It.apply(this,arguments)};function X0(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)t.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(n[a[l]]=e[a[l]]);return n}function xN(e,t,n){if(n||arguments.length===2)for(var a=0,l=t.length,r;a<l;a++)(r||!(a in t))&&(r||(r=Array.prototype.slice.call(t,0,a)),r[a]=t[a]);return e.concat(r||Array.prototype.slice.call(t))}var Do="right-scroll-bar-position",_o="width-before-scroll-bar",SN="with-scroll-bars-hidden",wN="--removed-body-scroll-bar-size";function Ic(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function EN(e,t){var n=p.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(a){var l=n.value;l!==a&&(n.value=a,n.callback(a,l))}}}})[0];return n.callback=t,n.facade}var AN=typeof window<"u"?p.useLayoutEffect:p.useEffect,fp=new WeakMap;function TN(e,t){var n=EN(null,function(a){return e.forEach(function(l){return Ic(l,a)})});return AN(function(){var a=fp.get(n);if(a){var l=new Set(a),r=new Set(e),i=n.current;l.forEach(function(o){r.has(o)||Ic(o,null)}),r.forEach(function(o){l.has(o)||Ic(o,i)})}fp.set(n,e)},[e]),n}function NN(e){return e}function CN(e,t){t===void 0&&(t=NN);var n=[],a=!1,l={read:function(){if(a)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(r){var i=t(r,a);return n.push(i),function(){n=n.filter(function(o){return o!==i})}},assignSyncMedium:function(r){for(a=!0;n.length;){var i=n;n=[],i.forEach(r)}n={push:function(o){return r(o)},filter:function(){return n}}},assignMedium:function(r){a=!0;var i=[];if(n.length){var o=n;n=[],o.forEach(r),i=n}var s=function(){var d=i;i=[],d.forEach(r)},u=function(){return Promise.resolve().then(s)};u(),n={push:function(d){i.push(d),u()},filter:function(d){return i=i.filter(d),n}}}};return l}function jN(e){e===void 0&&(e={});var t=CN(null);return t.options=It({async:!0,ssr:!1},e),t}var P0=function(e){var t=e.sideCar,n=X0(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var a=t.read();if(!a)throw new Error("Sidecar medium not found");return p.createElement(a,It({},n))};P0.isSideCarExport=!0;function RN(e,t){return e.useMedium(t),P0}var Z0=jN(),eu=function(){},tc=p.forwardRef(function(e,t){var n=p.useRef(null),a=p.useState({onScrollCapture:eu,onWheelCapture:eu,onTouchMoveCapture:eu}),l=a[0],r=a[1],i=e.forwardProps,o=e.children,s=e.className,u=e.removeScrollBar,d=e.enabled,m=e.shards,h=e.sideCar,f=e.noRelative,w=e.noIsolation,y=e.inert,x=e.allowPinchZoom,v=e.as,g=v===void 0?"div":v,b=e.gapMode,S=X0(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=h,T=TN([n,t]),A=It(It({},S),l);return p.createElement(p.Fragment,null,d&&p.createElement(E,{sideCar:Z0,removeScrollBar:u,shards:m,noRelative:f,noIsolation:w,inert:y,setCallbacks:r,allowPinchZoom:!!x,lockRef:n,gapMode:b}),i?p.cloneElement(p.Children.only(o),It(It({},A),{ref:T})):p.createElement(g,It({},A,{className:s,ref:T}),o))});tc.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};tc.classNames={fullWidth:_o,zeroRight:Do};var ON=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function MN(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=ON();return t&&e.setAttribute("nonce",t),e}function DN(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function _N(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var zN=function(){var e=0,t=null;return{add:function(n){e==0&&(t=MN())&&(DN(t,n),_N(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},UN=function(){var e=zN();return function(t,n){p.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},K0=function(){var e=UN(),t=function(n){var a=n.styles,l=n.dynamic;return e(a,l),null};return t},LN={left:0,top:0,right:0,gap:0},tu=function(e){return parseInt(e||"",10)||0},BN=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],a=t[e==="padding"?"paddingTop":"marginTop"],l=t[e==="padding"?"paddingRight":"marginRight"];return[tu(n),tu(a),tu(l)]},HN=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return LN;var t=BN(e),n=document.documentElement.clientWidth,a=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,a-n+t[2]-t[0])}},kN=K0(),Ql="data-scroll-locked",qN=function(e,t,n,a){var l=e.left,r=e.top,i=e.right,o=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(SN,` {
   overflow: hidden `).concat(a,`;
   padding-right: `).concat(o,"px ").concat(a,`;
  }
  body[`).concat(Ql,`] {
    overflow: hidden `).concat(a,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(a,";"),n==="margin"&&`
    padding-left: `.concat(l,`px;
    padding-top: `).concat(r,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(o,"px ").concat(a,`;
    `),n==="padding"&&"padding-right: ".concat(o,"px ").concat(a,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Do,` {
    right: `).concat(o,"px ").concat(a,`;
  }
  
  .`).concat(_o,` {
    margin-right: `).concat(o,"px ").concat(a,`;
  }
  
  .`).concat(Do," .").concat(Do,` {
    right: 0 `).concat(a,`;
  }
  
  .`).concat(_o," .").concat(_o,` {
    margin-right: 0 `).concat(a,`;
  }
  
  body[`).concat(Ql,`] {
    `).concat(wN,": ").concat(o,`px;
  }
`)},dp=function(){var e=parseInt(document.body.getAttribute(Ql)||"0",10);return isFinite(e)?e:0},GN=function(){p.useEffect(function(){return document.body.setAttribute(Ql,(dp()+1).toString()),function(){var e=dp()-1;e<=0?document.body.removeAttribute(Ql):document.body.setAttribute(Ql,e.toString())}},[])},YN=function(e){var t=e.noRelative,n=e.noImportant,a=e.gapMode,l=a===void 0?"margin":a;GN();var r=p.useMemo(function(){return HN(l)},[l]);return p.createElement(kN,{styles:qN(r,!t,l,n?"":"!important")})},df=!1;if(typeof window<"u")try{var mo=Object.defineProperty({},"passive",{get:function(){return df=!0,!0}});window.addEventListener("test",mo,mo),window.removeEventListener("test",mo,mo)}catch{df=!1}var ml=df?{passive:!1}:!1,VN=function(e){return e.tagName==="TEXTAREA"},F0=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!VN(e)&&n[t]==="visible")},QN=function(e){return F0(e,"overflowY")},XN=function(e){return F0(e,"overflowX")},hp=function(e,t){var n=t.ownerDocument,a=t;do{typeof ShadowRoot<"u"&&a instanceof ShadowRoot&&(a=a.host);var l=J0(e,a);if(l){var r=$0(e,a),i=r[1],o=r[2];if(i>o)return!0}a=a.parentNode}while(a&&a!==n.body);return!1},PN=function(e){var t=e.scrollTop,n=e.scrollHeight,a=e.clientHeight;return[t,n,a]},ZN=function(e){var t=e.scrollLeft,n=e.scrollWidth,a=e.clientWidth;return[t,n,a]},J0=function(e,t){return e==="v"?QN(t):XN(t)},$0=function(e,t){return e==="v"?PN(t):ZN(t)},KN=function(e,t){return e==="h"&&t==="rtl"?-1:1},FN=function(e,t,n,a,l){var r=KN(e,window.getComputedStyle(t).direction),i=r*a,o=n.target,s=t.contains(o),u=!1,d=i>0,m=0,h=0;do{if(!o)break;var f=$0(e,o),w=f[0],y=f[1],x=f[2],v=y-x-r*w;(w||v)&&J0(e,o)&&(m+=v,h+=w);var g=o.parentNode;o=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!s&&o!==document.body||s&&(t.contains(o)||t===o));return(d&&Math.abs(m)<1||!d&&Math.abs(h)<1)&&(u=!0),u},po=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},mp=function(e){return[e.deltaX,e.deltaY]},pp=function(e){return e&&"current"in e?e.current:e},JN=function(e,t){return e[0]===t[0]&&e[1]===t[1]},$N=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},WN=0,pl=[];function IN(e){var t=p.useRef([]),n=p.useRef([0,0]),a=p.useRef(),l=p.useState(WN++)[0],r=p.useState(K0)[0],i=p.useRef(e);p.useEffect(function(){i.current=e},[e]),p.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(l));var y=xN([e.lockRef.current],(e.shards||[]).map(pp),!0).filter(Boolean);return y.forEach(function(x){return x.classList.add("allow-interactivity-".concat(l))}),function(){document.body.classList.remove("block-interactivity-".concat(l)),y.forEach(function(x){return x.classList.remove("allow-interactivity-".concat(l))})}}},[e.inert,e.lockRef.current,e.shards]);var o=p.useCallback(function(y,x){if("touches"in y&&y.touches.length===2||y.type==="wheel"&&y.ctrlKey)return!i.current.allowPinchZoom;var v=po(y),g=n.current,b="deltaX"in y?y.deltaX:g[0]-v[0],S="deltaY"in y?y.deltaY:g[1]-v[1],E,T=y.target,A=Math.abs(b)>Math.abs(S)?"h":"v";if("touches"in y&&A==="h"&&T.type==="range")return!1;var C=hp(A,T);if(!C)return!0;if(C?E=A:(E=A==="v"?"h":"v",C=hp(A,T)),!C)return!1;if(!a.current&&"changedTouches"in y&&(b||S)&&(a.current=E),!E)return!0;var O=a.current||E;return FN(O,x,y,O==="h"?b:S)},[]),s=p.useCallback(function(y){var x=y;if(!(!pl.length||pl[pl.length-1]!==r)){var v="deltaY"in x?mp(x):po(x),g=t.current.filter(function(E){return E.name===x.type&&(E.target===x.target||x.target===E.shadowParent)&&JN(E.delta,v)})[0];if(g&&g.should){x.cancelable&&x.preventDefault();return}if(!g){var b=(i.current.shards||[]).map(pp).filter(Boolean).filter(function(E){return E.contains(x.target)}),S=b.length>0?o(x,b[0]):!i.current.noIsolation;S&&x.cancelable&&x.preventDefault()}}},[]),u=p.useCallback(function(y,x,v,g){var b={name:y,delta:x,target:v,should:g,shadowParent:eC(v)};t.current.push(b),setTimeout(function(){t.current=t.current.filter(function(S){return S!==b})},1)},[]),d=p.useCallback(function(y){n.current=po(y),a.current=void 0},[]),m=p.useCallback(function(y){u(y.type,mp(y),y.target,o(y,e.lockRef.current))},[]),h=p.useCallback(function(y){u(y.type,po(y),y.target,o(y,e.lockRef.current))},[]);p.useEffect(function(){return pl.push(r),e.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:h}),document.addEventListener("wheel",s,ml),document.addEventListener("touchmove",s,ml),document.addEventListener("touchstart",d,ml),function(){pl=pl.filter(function(y){return y!==r}),document.removeEventListener("wheel",s,ml),document.removeEventListener("touchmove",s,ml),document.removeEventListener("touchstart",d,ml)}},[]);var f=e.removeScrollBar,w=e.inert;return p.createElement(p.Fragment,null,w?p.createElement(r,{styles:$N(l)}):null,f?p.createElement(YN,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function eC(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const tC=RN(Z0,IN);var W0=p.forwardRef(function(e,t){return p.createElement(tc,It({},e,{ref:t,sideCar:tC}))});W0.classNames=tc.classNames;var nC=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},vl=new WeakMap,vo=new WeakMap,go={},nu=0,I0=function(e){return e&&(e.host||I0(e.parentNode))},aC=function(e,t){return t.map(function(n){if(e.contains(n))return n;var a=I0(n);return a&&e.contains(a)?a:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},lC=function(e,t,n,a){var l=aC(t,Array.isArray(e)?e:[e]);go[n]||(go[n]=new WeakMap);var r=go[n],i=[],o=new Set,s=new Set(l),u=function(m){!m||o.has(m)||(o.add(m),u(m.parentNode))};l.forEach(u);var d=function(m){!m||s.has(m)||Array.prototype.forEach.call(m.children,function(h){if(o.has(h))d(h);else try{var f=h.getAttribute(a),w=f!==null&&f!=="false",y=(vl.get(h)||0)+1,x=(r.get(h)||0)+1;vl.set(h,y),r.set(h,x),i.push(h),y===1&&w&&vo.set(h,!0),x===1&&h.setAttribute(n,"true"),w||h.setAttribute(a,"true")}catch(v){console.error("aria-hidden: cannot operate on ",h,v)}})};return d(t),o.clear(),nu++,function(){i.forEach(function(m){var h=vl.get(m)-1,f=r.get(m)-1;vl.set(m,h),r.set(m,f),h||(vo.has(m)||m.removeAttribute(a),vo.delete(m)),f||m.removeAttribute(n)}),nu--,nu||(vl=new WeakMap,vl=new WeakMap,vo=new WeakMap,go={})}},rC=function(e,t,n){n===void 0&&(n="data-aria-hidden");var a=Array.from(Array.isArray(e)?e:[e]),l=nC(e);return l?(a.push.apply(a,Array.from(l.querySelectorAll("[aria-live], script"))),lC(a,l,n,"aria-hidden")):function(){return null}},nc="Dialog",[eb,pj]=rl(nc),[iC,Pt]=eb(nc),tb=e=>{const{__scopeDialog:t,children:n,open:a,defaultOpen:l,onOpenChange:r,modal:i=!0}=e,o=p.useRef(null),s=p.useRef(null),[u,d]=Ad({prop:a,defaultProp:l??!1,onChange:r,caller:nc});return c.jsx(iC,{scope:t,triggerRef:o,contentRef:s,contentId:ni(),titleId:ni(),descriptionId:ni(),open:u,onOpenChange:d,onOpenToggle:p.useCallback(()=>d(m=>!m),[d]),modal:i,children:n})};tb.displayName=nc;var nb="DialogTrigger",ab=p.forwardRef((e,t)=>{const{__scopeDialog:n,...a}=e,l=Pt(nb,n),r=Le(t,l.triggerRef);return c.jsx(pe.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":Hd(l.open),...a,ref:r,onClick:ne(e.onClick,l.onOpenToggle)})});ab.displayName=nb;var Ld="DialogPortal",[oC,lb]=eb(Ld,{forceMount:void 0}),rb=e=>{const{__scopeDialog:t,forceMount:n,children:a,container:l}=e,r=Pt(Ld,t);return c.jsx(oC,{scope:t,forceMount:n,children:p.Children.map(a,i=>c.jsx(fn,{present:n||r.open,children:c.jsx(Ky,{asChild:!0,container:l,children:i})}))})};rb.displayName=Ld;var ws="DialogOverlay",ib=p.forwardRef((e,t)=>{const n=lb(ws,e.__scopeDialog),{forceMount:a=n.forceMount,...l}=e,r=Pt(ws,e.__scopeDialog);return r.modal?c.jsx(fn,{present:a||r.open,children:c.jsx(cC,{...l,ref:t})}):null});ib.displayName=ws;var sC=gi("DialogOverlay.RemoveScroll"),cC=p.forwardRef((e,t)=>{const{__scopeDialog:n,...a}=e,l=Pt(ws,n);return c.jsx(W0,{as:sC,allowPinchZoom:!0,shards:[l.contentRef],children:c.jsx(pe.div,{"data-state":Hd(l.open),...a,ref:t,style:{pointerEvents:"auto",...a.style}})})}),el="DialogContent",ob=p.forwardRef((e,t)=>{const n=lb(el,e.__scopeDialog),{forceMount:a=n.forceMount,...l}=e,r=Pt(el,e.__scopeDialog);return c.jsx(fn,{present:a||r.open,children:r.modal?c.jsx(uC,{...l,ref:t}):c.jsx(fC,{...l,ref:t})})});ob.displayName=el;var uC=p.forwardRef((e,t)=>{const n=Pt(el,e.__scopeDialog),a=p.useRef(null),l=Le(t,n.contentRef,a);return p.useEffect(()=>{const r=a.current;if(r)return rC(r)},[]),c.jsx(sb,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:ne(e.onCloseAutoFocus,r=>{var i;r.preventDefault(),(i=n.triggerRef.current)==null||i.focus()}),onPointerDownOutside:ne(e.onPointerDownOutside,r=>{const i=r.detail.originalEvent,o=i.button===0&&i.ctrlKey===!0;(i.button===2||o)&&r.preventDefault()}),onFocusOutside:ne(e.onFocusOutside,r=>r.preventDefault())})}),fC=p.forwardRef((e,t)=>{const n=Pt(el,e.__scopeDialog),a=p.useRef(!1),l=p.useRef(!1);return c.jsx(sb,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:r=>{var i,o;(i=e.onCloseAutoFocus)==null||i.call(e,r),r.defaultPrevented||(a.current||(o=n.triggerRef.current)==null||o.focus(),r.preventDefault()),a.current=!1,l.current=!1},onInteractOutside:r=>{var s,u;(s=e.onInteractOutside)==null||s.call(e,r),r.defaultPrevented||(a.current=!0,r.detail.originalEvent.type==="pointerdown"&&(l.current=!0));const i=r.target;((u=n.triggerRef.current)==null?void 0:u.contains(i))&&r.preventDefault(),r.detail.originalEvent.type==="focusin"&&l.current&&r.preventDefault()}})}),sb=p.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:a,onOpenAutoFocus:l,onCloseAutoFocus:r,...i}=e,o=Pt(el,n),s=p.useRef(null),u=Le(t,s);return bN(),c.jsxs(c.Fragment,{children:[c.jsx(V0,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:l,onUnmountAutoFocus:r,children:c.jsx(pd,{role:"dialog",id:o.contentId,"aria-describedby":o.descriptionId,"aria-labelledby":o.titleId,"data-state":Hd(o.open),...i,ref:u,onDismiss:()=>o.onOpenChange(!1)})}),c.jsxs(c.Fragment,{children:[c.jsx(dC,{titleId:o.titleId}),c.jsx(mC,{contentRef:s,descriptionId:o.descriptionId})]})]})}),Bd="DialogTitle",cb=p.forwardRef((e,t)=>{const{__scopeDialog:n,...a}=e,l=Pt(Bd,n);return c.jsx(pe.h2,{id:l.titleId,...a,ref:t})});cb.displayName=Bd;var ub="DialogDescription",fb=p.forwardRef((e,t)=>{const{__scopeDialog:n,...a}=e,l=Pt(ub,n);return c.jsx(pe.p,{id:l.descriptionId,...a,ref:t})});fb.displayName=ub;var db="DialogClose",hb=p.forwardRef((e,t)=>{const{__scopeDialog:n,...a}=e,l=Pt(db,n);return c.jsx(pe.button,{type:"button",...a,ref:t,onClick:ne(e.onClick,()=>l.onOpenChange(!1))})});hb.displayName=db;function Hd(e){return e?"open":"closed"}var mb="DialogTitleWarning",[vj,pb]=jw(mb,{contentName:el,titleName:Bd,docsSlug:"dialog"}),dC=({titleId:e})=>{const t=pb(mb),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return p.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},hC="DialogDescriptionWarning",mC=({contentRef:e,descriptionId:t})=>{const a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${pb(hC).contentName}}.`;return p.useEffect(()=>{var r;const l=(r=e.current)==null?void 0:r.getAttribute("aria-describedby");t&&l&&(document.getElementById(t)||console.warn(a))},[a,e,t]),null},pC=tb,vC=ab,gC=rb,vb=ib,gb=ob,yb=cb,bb=fb,yC=hb;const bC=pC,xC=vC,SC=gC,xb=p.forwardRef(({className:e,...t},n)=>c.jsx(vb,{className:ae("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t,ref:n}));xb.displayName=vb.displayName;const wC=Ws("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),Sb=p.forwardRef(({side:e="right",className:t,children:n,...a},l)=>c.jsxs(SC,{children:[c.jsx(xb,{}),c.jsxs(gb,{ref:l,className:ae(wC({side:e}),t),...a,children:[n,c.jsxs(yC,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[c.jsx(uN,{className:"h-4 w-4"}),c.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Sb.displayName=gb.displayName;const EC=p.forwardRef(({className:e,...t},n)=>c.jsx(yb,{ref:n,className:ae("text-lg font-semibold text-foreground",e),...t}));EC.displayName=yb.displayName;const AC=p.forwardRef(({className:e,...t},n)=>c.jsx(bb,{ref:n,className:ae("text-sm text-muted-foreground",e),...t}));AC.displayName=bb.displayName;const TC=[{id:"overview",title:"Overview"},{id:"architecture",title:"System Architecture"},{id:"technologies",title:"Technologies Used"},{id:"user-journey",title:"User Journey & Features"},{id:"backend-docs",title:"Backend API Documentation"},{id:"frontend-apps",title:"Frontend Applications"},{id:"ai-features",title:"AI & Machine Learning"},{id:"installation",title:"Installation & Setup"},{id:"api-endpoints",title:"API Endpoints"},{id:"deployment",title:"Deployment"}];function NC({activeSection:e,onSectionClick:t}){const[n,a]=p.useState(!1),l=()=>c.jsxs("div",{className:"flex flex-col h-full",children:[c.jsxs("div",{className:"p-6 border-b",children:[c.jsx("h2",{className:"text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:"Skill Sage"}),c.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"AI-Powered Job Matching"})]}),c.jsx(Ud,{className:"flex-1 p-4",children:c.jsx("nav",{className:"space-y-2",children:TC.map(r=>c.jsx(Wt,{variant:e===r.id?"secondary":"ghost",className:"w-full justify-start text-left",onClick:()=>{t(r.id),a(!1)},children:r.title},r.id))})}),c.jsxs("div",{className:"p-4 border-t space-y-2",children:[c.jsx(Wt,{variant:"outline",className:"w-full justify-start",asChild:!0,children:c.jsxs("a",{href:"https://github.com/Emmanuel-Aggrey/skill_sage_backend",target:"_blank",rel:"noopener noreferrer",children:[c.jsx(Fc,{className:"mr-2 h-4 w-4"}),"Backend Repo",c.jsx(Kc,{className:"ml-auto h-4 w-4"})]})}),c.jsx(Wt,{variant:"outline",className:"w-full justify-start",asChild:!0,children:c.jsxs("a",{href:"https://github.com/Emmanuel-Aggrey/skill_sage_mobile",target:"_blank",rel:"noopener noreferrer",children:[c.jsx(Fc,{className:"mr-2 h-4 w-4"}),"Mobile Repo",c.jsx(Kc,{className:"ml-auto h-4 w-4"})]})}),c.jsx(Wt,{variant:"outline",className:"w-full justify-start",asChild:!0,children:c.jsxs("a",{href:"https://github.com/Emmanuel-Aggrey/skill_sage_dashboard",target:"_blank",rel:"noopener noreferrer",children:[c.jsx(Fc,{className:"mr-2 h-4 w-4"}),"Dashboard Repo",c.jsx(Kc,{className:"ml-auto h-4 w-4"})]})})]})]});return c.jsxs(c.Fragment,{children:[c.jsxs(bC,{open:n,onOpenChange:a,children:[c.jsx(xC,{asChild:!0,children:c.jsx(Wt,{variant:"outline",size:"icon",className:"md:hidden fixed top-4 left-4 z-50",children:c.jsx(lN,{className:"h-4 w-4"})})}),c.jsx(Sb,{side:"left",className:"w-80 p-0",children:c.jsx(l,{})})]}),c.jsx("div",{className:"hidden md:flex w-80 border-r bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 fixed left-0 top-0 h-full",children:c.jsx(l,{})})]})}const ye=p.forwardRef(({className:e,...t},n)=>c.jsx("div",{ref:n,className:ae("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));ye.displayName="Card";const be=p.forwardRef(({className:e,...t},n)=>c.jsx("div",{ref:n,className:ae("flex flex-col space-y-1.5 p-6",e),...t}));be.displayName="CardHeader";const xe=p.forwardRef(({className:e,...t},n)=>c.jsx("h3",{ref:n,className:ae("text-2xl font-semibold leading-none tracking-tight",e),...t}));xe.displayName="CardTitle";const De=p.forwardRef(({className:e,...t},n)=>c.jsx("p",{ref:n,className:ae("text-sm text-muted-foreground",e),...t}));De.displayName="CardDescription";const _e=p.forwardRef(({className:e,...t},n)=>c.jsx("div",{ref:n,className:ae("p-6 pt-0",e),...t}));_e.displayName="CardContent";const CC=p.forwardRef(({className:e,...t},n)=>c.jsx("div",{ref:n,className:ae("flex items-center p-6 pt-0",e),...t}));CC.displayName="CardFooter";const jC=Ws("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function Ua({className:e,variant:t,...n}){return c.jsx("div",{className:ae(jC({variant:t}),e),...n})}function RC(){const e=[{icon:c.jsx(eN,{className:"h-6 w-6"}),title:"AI-Powered Job Matching",description:"Advanced recommendation system using clustering and Jaccard similarity"},{icon:c.jsx(fN,{className:"h-6 w-6"}),title:"Resume Analysis",description:"Intelligent parsing and skill extraction from resumes"},{icon:c.jsx(wi,{className:"h-6 w-6"}),title:"Real-time Job Scraping",description:"Automated job collection from multiple sources"},{icon:c.jsx(cN,{className:"h-6 w-6"}),title:"Skill Transferability Analysis",description:"Graph theory-based skill mapping across occupations"}],t=[{icon:c.jsx(Ss,{className:"h-8 w-8 text-blue-600"}),title:"Flutter Mobile App",description:"Cross-platform mobile application for job seekers",tech:["Flutter","Dart","Riverpod"]},{icon:c.jsx(wi,{className:"h-8 w-8 text-green-600"}),title:"React Dashboard",description:"Administrative web interface for job management",tech:["React","TypeScript","CSS3"]},{icon:c.jsx(Ei,{className:"h-8 w-8 text-purple-600"}),title:"FastAPI Backend",description:"High-performance API server with AI-powered matching algorithms",tech:["FastAPI","Python","PostgreSQL"]}];return c.jsxs("section",{id:"overview",className:"space-y-8",children:[c.jsxs("div",{children:[c.jsx("h1",{className:"text-4xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:"Skill Sage Documentation"}),c.jsx("p",{className:"text-xl text-muted-foreground mb-6",children:"AI-Powered Job Matching Platform"}),c.jsx("p",{className:"text-lg leading-relaxed",children:"Skill Sage is a comprehensive AI-powered job matching platform that leverages natural language processing (NLP), machine learning, and advanced recommendation systems to connect job seekers with relevant opportunities. The platform consists of three main components working together to provide an intelligent job matching experience."})]}),c.jsxs("div",{children:[c.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"Key Features"}),c.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:e.map((n,a)=>c.jsxs(ye,{children:[c.jsx(be,{className:"pb-3",children:c.jsxs("div",{className:"flex items-center gap-3",children:[c.jsx("div",{className:"p-2 bg-primary/10 rounded-lg text-primary",children:n.icon}),c.jsx(xe,{className:"text-lg",children:n.title})]})}),c.jsx(_e,{children:c.jsx(De,{children:n.description})})]},a))})]}),c.jsxs("div",{children:[c.jsx("h2",{className:"text-2xl font-semibold mb-4",children:"System Components"}),c.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:t.map((n,a)=>c.jsxs(ye,{className:"h-full",children:[c.jsxs(be,{children:[c.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[n.icon,c.jsx(xe,{className:"text-lg",children:n.title})]}),c.jsx(De,{children:n.description})]}),c.jsx(_e,{children:c.jsx("div",{className:"flex flex-wrap gap-2",children:n.tech.map((l,r)=>c.jsx(Ua,{variant:"secondary",children:l},r))})})]},a))})]})]})}function OC(){return c.jsxs("section",{id:"architecture",className:"space-y-8",children:[c.jsxs("div",{children:[c.jsx("h2",{className:"text-3xl font-bold mb-4",children:"System Architecture"}),c.jsx("p",{className:"text-lg text-muted-foreground mb-6",children:"Skill Sage follows a modern microservices architecture with clear separation of concerns and scalable components."})]}),c.jsxs("div",{className:"bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-950 p-8 rounded-lg",children:[c.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8",children:[c.jsx(ye,{className:"text-center",children:c.jsxs(be,{children:[c.jsx("div",{className:"flex justify-center mb-2",children:c.jsx(Ss,{className:"h-8 w-8 text-blue-600"})}),c.jsx(xe,{children:"Flutter App"}),c.jsx(De,{children:"Job Seekers"})]})}),c.jsx(ye,{className:"text-center",children:c.jsxs(be,{children:[c.jsx("div",{className:"flex justify-center mb-2",children:c.jsx(wi,{className:"h-8 w-8 text-green-600"})}),c.jsx(xe,{children:"React Dashboard"}),c.jsx(De,{children:"Administrators"})]})}),c.jsx(ye,{className:"text-center",children:c.jsxs(be,{children:[c.jsx("div",{className:"flex justify-center mb-2",children:c.jsx(Ei,{className:"h-8 w-8 text-orange-600"})}),c.jsx(xe,{children:"Web Scrapers"}),c.jsx(De,{children:"External APIs"})]})})]}),c.jsx("div",{className:"flex justify-center mb-8",children:c.jsx(ep,{className:"h-8 w-8 text-muted-foreground"})}),c.jsxs(ye,{className:"mb-8",children:[c.jsxs(be,{className:"text-center",children:[c.jsx("div",{className:"flex justify-center mb-2",children:c.jsx(Ei,{className:"h-10 w-10 text-purple-600"})}),c.jsx(xe,{className:"text-xl",children:"FastAPI Backend (Port 8004)"}),c.jsx(De,{children:"High-performance API server with AI services"})]}),c.jsx(_e,{children:c.jsxs("div",{className:"bg-white dark:bg-slate-800 p-4 rounded-lg",children:[c.jsx("h4",{className:"font-semibold mb-3 text-center",children:"AI/ML Services"}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[c.jsxs("div",{className:"text-center p-2 bg-blue-50 dark:bg-blue-950 rounded",children:[c.jsx("strong",{children:"LLM (Gemini 2.0)"}),c.jsx("br",{}),"Natural Language Processing"]}),c.jsxs("div",{className:"text-center p-2 bg-green-50 dark:bg-green-950 rounded",children:[c.jsx("strong",{children:"Job Matching"}),c.jsx("br",{}),"AI-Powered Recommendations"]}),c.jsxs("div",{className:"text-center p-2 bg-purple-50 dark:bg-purple-950 rounded",children:[c.jsx("strong",{children:"Skill Analysis"}),c.jsx("br",{}),"Transferability & Clustering"]})]})]})})]}),c.jsx("div",{className:"flex justify-center mb-8",children:c.jsx(ep,{className:"h-8 w-8 text-muted-foreground"})}),c.jsx(ye,{className:"text-center",children:c.jsxs(be,{children:[c.jsx("div",{className:"flex justify-center mb-2",children:c.jsx(aN,{className:"h-10 w-10 text-indigo-600"})}),c.jsx(xe,{className:"text-xl",children:"PostgreSQL Database"}),c.jsx(De,{children:"User Data & Jobs Storage"})]})})]}),c.jsxs("div",{children:[c.jsx("h3",{className:"text-2xl font-semibold mb-4",children:"Architecture Benefits"}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[c.jsxs(ye,{children:[c.jsx(be,{children:c.jsx(xe,{className:"text-lg",children:"Scalability"})}),c.jsx(_e,{children:c.jsx(De,{children:"Microservices architecture allows independent scaling of components based on demand."})})]}),c.jsxs(ye,{children:[c.jsx(be,{children:c.jsx(xe,{className:"text-lg",children:"Maintainability"})}),c.jsx(_e,{children:c.jsx(De,{children:"Clear separation of concerns makes the system easier to maintain and update."})})]}),c.jsxs(ye,{children:[c.jsx(be,{children:c.jsx(xe,{className:"text-lg",children:"Performance"})}),c.jsx(_e,{children:c.jsx(De,{children:"FastAPI backend with async operations ensures high performance and low latency."})})]}),c.jsxs(ye,{children:[c.jsx(be,{children:c.jsx(xe,{className:"text-lg",children:"Flexibility"})}),c.jsx(_e,{children:c.jsx(De,{children:"Multiple client applications can consume the same API with different interfaces."})})]})]})]})]})}function MC(e){const t=e+"CollectionProvider",[n,a]=rl(t),[l,r]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=y=>{const{scope:x,children:v}=y,g=D.useRef(null),b=D.useRef(new Map).current;return c.jsx(l,{scope:x,itemMap:b,collectionRef:g,children:v})};i.displayName=t;const o=e+"CollectionSlot",s=gi(o),u=D.forwardRef((y,x)=>{const{scope:v,children:g}=y,b=r(o,v),S=Le(x,b.collectionRef);return c.jsx(s,{ref:S,children:g})});u.displayName=o;const d=e+"CollectionItemSlot",m="data-radix-collection-item",h=gi(d),f=D.forwardRef((y,x)=>{const{scope:v,children:g,...b}=y,S=D.useRef(null),E=Le(x,S),T=r(d,v);return D.useEffect(()=>(T.itemMap.set(S,{ref:S,...b}),()=>void T.itemMap.delete(S))),c.jsx(h,{[m]:"",ref:E,children:g})});f.displayName=d;function w(y){const x=r(e+"CollectionConsumer",y);return D.useCallback(()=>{const g=x.collectionRef.current;if(!g)return[];const b=Array.from(g.querySelectorAll(`[${m}]`));return Array.from(x.itemMap.values()).sort((T,A)=>b.indexOf(T.ref.current)-b.indexOf(A.ref.current))},[x.collectionRef,x.itemMap])}return[{Provider:i,Slot:u,ItemSlot:f},w,a]}var au="rovingFocusGroup.onEntryFocus",DC={bubbles:!1,cancelable:!0},Vi="RovingFocusGroup",[hf,wb,_C]=MC(Vi),[zC,Eb]=rl(Vi,[_C]),[UC,LC]=zC(Vi),Ab=p.forwardRef((e,t)=>c.jsx(hf.Provider,{scope:e.__scopeRovingFocusGroup,children:c.jsx(hf.Slot,{scope:e.__scopeRovingFocusGroup,children:c.jsx(BC,{...e,ref:t})})}));Ab.displayName=Vi;var BC=p.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:a,loop:l=!1,dir:r,currentTabStopId:i,defaultCurrentTabStopId:o,onCurrentTabStopIdChange:s,onEntryFocus:u,preventScrollOnEntryFocus:d=!1,...m}=e,h=p.useRef(null),f=Le(t,h),w=Od(r),[y,x]=Ad({prop:i,defaultProp:o??null,onChange:s,caller:Vi}),[v,g]=p.useState(!1),b=it(u),S=wb(n),E=p.useRef(!1),[T,A]=p.useState(0);return p.useEffect(()=>{const C=h.current;if(C)return C.addEventListener(au,b),()=>C.removeEventListener(au,b)},[b]),c.jsx(UC,{scope:n,orientation:a,dir:w,loop:l,currentTabStopId:y,onItemFocus:p.useCallback(C=>x(C),[x]),onItemShiftTab:p.useCallback(()=>g(!0),[]),onFocusableItemAdd:p.useCallback(()=>A(C=>C+1),[]),onFocusableItemRemove:p.useCallback(()=>A(C=>C-1),[]),children:c.jsx(pe.div,{tabIndex:v||T===0?-1:0,"data-orientation":a,...m,ref:f,style:{outline:"none",...e.style},onMouseDown:ne(e.onMouseDown,()=>{E.current=!0}),onFocus:ne(e.onFocus,C=>{const O=!E.current;if(C.target===C.currentTarget&&O&&!v){const M=new CustomEvent(au,DC);if(C.currentTarget.dispatchEvent(M),!M.defaultPrevented){const B=S().filter(R=>R.focusable),L=B.find(R=>R.active),$=B.find(R=>R.id===y),ce=[L,$,...B].filter(Boolean).map(R=>R.ref.current);Cb(ce,d)}}E.current=!1}),onBlur:ne(e.onBlur,()=>g(!1))})})}),Tb="RovingFocusGroupItem",Nb=p.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:a=!0,active:l=!1,tabStopId:r,children:i,...o}=e,s=ni(),u=r||s,d=LC(Tb,n),m=d.currentTabStopId===u,h=wb(n),{onFocusableItemAdd:f,onFocusableItemRemove:w,currentTabStopId:y}=d;return p.useEffect(()=>{if(a)return f(),()=>w()},[a,f,w]),c.jsx(hf.ItemSlot,{scope:n,id:u,focusable:a,active:l,children:c.jsx(pe.span,{tabIndex:m?0:-1,"data-orientation":d.orientation,...o,ref:t,onMouseDown:ne(e.onMouseDown,x=>{a?d.onItemFocus(u):x.preventDefault()}),onFocus:ne(e.onFocus,()=>d.onItemFocus(u)),onKeyDown:ne(e.onKeyDown,x=>{if(x.key==="Tab"&&x.shiftKey){d.onItemShiftTab();return}if(x.target!==x.currentTarget)return;const v=qC(x,d.orientation,d.dir);if(v!==void 0){if(x.metaKey||x.ctrlKey||x.altKey||x.shiftKey)return;x.preventDefault();let b=h().filter(S=>S.focusable).map(S=>S.ref.current);if(v==="last")b.reverse();else if(v==="prev"||v==="next"){v==="prev"&&b.reverse();const S=b.indexOf(x.currentTarget);b=d.loop?GC(b,S+1):b.slice(S+1)}setTimeout(()=>Cb(b))}}),children:typeof i=="function"?i({isCurrentTabStop:m,hasTabStop:y!=null}):i})})});Nb.displayName=Tb;var HC={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function kC(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function qC(e,t,n){const a=kC(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(a))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(a)))return HC[a]}function Cb(e,t=!1){const n=document.activeElement;for(const a of e)if(a===n||(a.focus({preventScroll:t}),document.activeElement!==n))return}function GC(e,t){return e.map((n,a)=>e[(t+a)%e.length])}var YC=Ab,VC=Nb,ac="Tabs",[QC,gj]=rl(ac,[Eb]),jb=Eb(),[XC,kd]=QC(ac),Rb=p.forwardRef((e,t)=>{const{__scopeTabs:n,value:a,onValueChange:l,defaultValue:r,orientation:i="horizontal",dir:o,activationMode:s="automatic",...u}=e,d=Od(o),[m,h]=Ad({prop:a,onChange:l,defaultProp:r??"",caller:ac});return c.jsx(XC,{scope:n,baseId:ni(),value:m,onValueChange:h,orientation:i,dir:d,activationMode:s,children:c.jsx(pe.div,{dir:d,"data-orientation":i,...u,ref:t})})});Rb.displayName=ac;var Ob="TabsList",Mb=p.forwardRef((e,t)=>{const{__scopeTabs:n,loop:a=!0,...l}=e,r=kd(Ob,n),i=jb(n);return c.jsx(YC,{asChild:!0,...i,orientation:r.orientation,dir:r.dir,loop:a,children:c.jsx(pe.div,{role:"tablist","aria-orientation":r.orientation,...l,ref:t})})});Mb.displayName=Ob;var Db="TabsTrigger",_b=p.forwardRef((e,t)=>{const{__scopeTabs:n,value:a,disabled:l=!1,...r}=e,i=kd(Db,n),o=jb(n),s=Lb(i.baseId,a),u=Bb(i.baseId,a),d=a===i.value;return c.jsx(VC,{asChild:!0,...o,focusable:!l,active:d,children:c.jsx(pe.button,{type:"button",role:"tab","aria-selected":d,"aria-controls":u,"data-state":d?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:s,...r,ref:t,onMouseDown:ne(e.onMouseDown,m=>{!l&&m.button===0&&m.ctrlKey===!1?i.onValueChange(a):m.preventDefault()}),onKeyDown:ne(e.onKeyDown,m=>{[" ","Enter"].includes(m.key)&&i.onValueChange(a)}),onFocus:ne(e.onFocus,()=>{const m=i.activationMode!=="manual";!d&&!l&&m&&i.onValueChange(a)})})})});_b.displayName=Db;var zb="TabsContent",Ub=p.forwardRef((e,t)=>{const{__scopeTabs:n,value:a,forceMount:l,children:r,...i}=e,o=kd(zb,n),s=Lb(o.baseId,a),u=Bb(o.baseId,a),d=a===o.value,m=p.useRef(d);return p.useEffect(()=>{const h=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(h)},[]),c.jsx(fn,{present:l||d,children:({present:h})=>c.jsx(pe.div,{"data-state":d?"active":"inactive","data-orientation":o.orientation,role:"tabpanel","aria-labelledby":s,hidden:!h,id:u,tabIndex:0,...i,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:h&&r})})});Ub.displayName=zb;function Lb(e,t){return`${e}-trigger-${t}`}function Bb(e,t){return`${e}-content-${t}`}var PC=Rb,Hb=Mb,kb=_b,qb=Ub;const Gb=PC,qd=p.forwardRef(({className:e,...t},n)=>c.jsx(Hb,{ref:n,className:ae("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));qd.displayName=Hb.displayName;const xn=p.forwardRef(({className:e,...t},n)=>c.jsx(kb,{ref:n,className:ae("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));xn.displayName=kb.displayName;const Sn=p.forwardRef(({className:e,...t},n)=>c.jsx(qb,{ref:n,className:ae("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));Sn.displayName=qb.displayName;const ZC=Ws("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),kr=p.forwardRef(({className:e,variant:t,...n},a)=>c.jsx("div",{ref:a,role:"alert",className:ae(ZC({variant:t}),e),...n}));kr.displayName="Alert";const KC=p.forwardRef(({className:e,...t},n)=>c.jsx("h5",{ref:n,className:ae("mb-1 font-medium leading-none tracking-tight",e),...t}));KC.displayName="AlertTitle";const qr=p.forwardRef(({className:e,...t},n)=>c.jsx("div",{ref:n,className:ae("text-sm [&_p]:leading-relaxed",e),...t}));qr.displayName="AlertDescription";function At({code:e,language:t="bash",title:n}){const[a,l]=p.useState(!1),r=async()=>{try{await navigator.clipboard.writeText(e),l(!0),setTimeout(()=>l(!1),2e3)}catch(i){console.error("Failed to copy text: ",i)}};return c.jsxs("div",{className:"relative group",children:[n&&c.jsx("div",{className:"bg-muted px-4 py-2 text-sm font-medium border-b",children:n}),c.jsxs("div",{className:"relative",children:[c.jsx("pre",{className:"bg-slate-900 text-slate-100 p-4 rounded-lg overflow-x-auto text-sm",children:c.jsx("code",{className:`language-${t}`,children:e})}),c.jsx(Wt,{size:"sm",variant:"secondary",className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity",onClick:r,children:a?c.jsx(tN,{className:"h-4 w-4"}):c.jsx(nN,{className:"h-4 w-4"})})]})]})}function FC(){return c.jsxs("section",{id:"installation",className:"space-y-8",children:[c.jsxs("div",{children:[c.jsx("h2",{className:"text-3xl font-bold mb-4",children:"Installation & Setup"}),c.jsx("p",{className:"text-lg text-muted-foreground mb-6",children:"Follow these step-by-step instructions to set up the complete Skill Sage platform on your local machine."})]}),c.jsxs(kr,{children:[c.jsx(sN,{className:"h-4 w-4"}),c.jsx(qr,{children:"Make sure you have Python 3.7+, Node.js 16+, and Flutter SDK installed before proceeding."})]}),c.jsxs(Gb,{defaultValue:"backend",className:"w-full",children:[c.jsxs(qd,{className:"grid w-full grid-cols-3",children:[c.jsxs(xn,{value:"backend",className:"flex items-center gap-2",children:[c.jsx(Ei,{className:"h-4 w-4"}),"Backend Setup"]}),c.jsxs(xn,{value:"mobile",className:"flex items-center gap-2",children:[c.jsx(Ss,{className:"h-4 w-4"}),"Flutter App"]}),c.jsxs(xn,{value:"dashboard",className:"flex items-center gap-2",children:[c.jsx(wi,{className:"h-4 w-4"}),"React Dashboard"]})]}),c.jsx(Sn,{value:"backend",className:"space-y-6",children:c.jsxs(ye,{children:[c.jsxs(be,{children:[c.jsxs(xe,{className:"flex items-center gap-2",children:[c.jsx(Ei,{className:"h-5 w-5"}),"FastAPI Backend Setup"]}),c.jsx(De,{children:"Set up the Python backend server with AI services and database integration."})]}),c.jsxs(_e,{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("h4",{className:"text-lg font-semibold mb-3",children:"1. Clone and Setup Environment"}),c.jsx(At,{code:`# Clone the repository
git clone <repository-url>
cd skill_sage

# Create virtual environment
python3 -m venv venv
source venv/bin/activate  # On Windows: .\\venv\\Scripts\\activate

# Install dependencies
pip install -r requirements.txt`,language:"bash"})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"text-lg font-semibold mb-3",children:"2. Environment Configuration"}),c.jsx(At,{code:`# Create .env file with required variables
GEMINI_API_KEY_01=your_gemini_api_key_1
GEMINI_API_KEY_02=your_gemini_api_key_2
DATABASE_URL=postgresql://user:password@localhost/skill_sage
SECRET_KEY=your_secret_key`,language:"bash",title:".env file"}),c.jsx(kr,{className:"mt-3",children:c.jsx(qr,{children:"You'll need to obtain Gemini API keys from Google AI Studio to enable AI features."})})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"text-lg font-semibold mb-3",children:"3. Database Setup"}),c.jsx(At,{code:`# Initialize database
python -c "from db.connection import initDB; initDB()"

# Run migrations (if any)
alembic upgrade head`,language:"bash"})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"text-lg font-semibold mb-3",children:"4. Start the Server"}),c.jsx(At,{code:`# Start the server
uvicorn main:app --reload --host 0.0.0.0 --port 8004`,language:"bash"}),c.jsx("div",{className:"mt-3",children:c.jsx(Ua,{variant:"secondary",children:"Server will be available at http://localhost:8004"})})]})]})]})}),c.jsx(Sn,{value:"mobile",className:"space-y-6",children:c.jsxs(ye,{children:[c.jsxs(be,{children:[c.jsxs(xe,{className:"flex items-center gap-2",children:[c.jsx(Ss,{className:"h-5 w-5"}),"Flutter Mobile App Setup"]}),c.jsx(De,{children:"Set up the cross-platform mobile application for job seekers."})]}),c.jsxs(_e,{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("h4",{className:"text-lg font-semibold mb-3",children:"1. Navigate and Install Dependencies"}),c.jsx(At,{code:`# Navigate to Flutter directory
cd Skill-Sage

# Install dependencies
flutter pub get`,language:"bash"})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"text-lg font-semibold mb-3",children:"2. Configure Environment"}),c.jsx(At,{code:`# Create .env file in assets folder
API_BASE_URL=http://localhost:8004`,language:"bash",title:"assets/.env"})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"text-lg font-semibold mb-3",children:"3. Run the Application"}),c.jsx(At,{code:`# Run the app
flutter run`,language:"bash"}),c.jsx(kr,{className:"mt-3",children:c.jsx(qr,{children:"Make sure you have an Android emulator running or a physical device connected."})})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"text-lg font-semibold mb-3",children:"Key Features"}),c.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[c.jsx(Ua,{variant:"outline",children:"Cross-platform compatibility"}),c.jsx(Ua,{variant:"outline",children:"Real-time job matching"}),c.jsx(Ua,{variant:"outline",children:"Resume upload & parsing"}),c.jsx(Ua,{variant:"outline",children:"Offline support"})]})]})]})]})}),c.jsx(Sn,{value:"dashboard",className:"space-y-6",children:c.jsxs(ye,{children:[c.jsxs(be,{children:[c.jsxs(xe,{className:"flex items-center gap-2",children:[c.jsx(wi,{className:"h-5 w-5"}),"React Dashboard Setup"]}),c.jsx(De,{children:"Set up the administrative web interface for managing jobs and users."})]}),c.jsxs(_e,{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("h4",{className:"text-lg font-semibold mb-3",children:"1. Setup and Install"}),c.jsx(At,{code:`# Navigate to dashboard directory
cd dashboard

# Install dependencies
npm install

# Start development server
npm start`,language:"bash"})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"text-lg font-semibold mb-3",children:"Admin Features"}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[c.jsxs("div",{className:"p-4 bg-muted rounded-lg",children:[c.jsx("h5",{className:"font-semibold mb-2",children:"Job Management"}),c.jsx("p",{className:"text-sm text-muted-foreground",children:"Enable/disable job postings and manage listings"})]}),c.jsxs("div",{className:"p-4 bg-muted rounded-lg",children:[c.jsx("h5",{className:"font-semibold mb-2",children:"User Analytics"}),c.jsx("p",{className:"text-sm text-muted-foreground",children:"View user engagement and platform statistics"})]}),c.jsxs("div",{className:"p-4 bg-muted rounded-lg",children:[c.jsx("h5",{className:"font-semibold mb-2",children:"Content Management"}),c.jsx("p",{className:"text-sm text-muted-foreground",children:"Manage courses and educational content"})]}),c.jsxs("div",{className:"p-4 bg-muted rounded-lg",children:[c.jsx("h5",{className:"font-semibold mb-2",children:"System Monitoring"}),c.jsx("p",{className:"text-sm text-muted-foreground",children:"API performance and error tracking"})]})]})]}),c.jsx(kr,{children:c.jsx(qr,{children:"The dashboard will be available at http://localhost:3000 after starting the development server."})})]})]})})]})]})}const Yb=p.forwardRef(({className:e,...t},n)=>c.jsx("div",{className:"relative w-full overflow-auto",children:c.jsx("table",{ref:n,className:ae("w-full caption-bottom text-sm",e),...t})}));Yb.displayName="Table";const Vb=p.forwardRef(({className:e,...t},n)=>c.jsx("thead",{ref:n,className:ae("[&_tr]:border-b",e),...t}));Vb.displayName="TableHeader";const Qb=p.forwardRef(({className:e,...t},n)=>c.jsx("tbody",{ref:n,className:ae("[&_tr:last-child]:border-0",e),...t}));Qb.displayName="TableBody";const JC=p.forwardRef(({className:e,...t},n)=>c.jsx("tfoot",{ref:n,className:ae("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t}));JC.displayName="TableFooter";const mf=p.forwardRef(({className:e,...t},n)=>c.jsx("tr",{ref:n,className:ae("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));mf.displayName="TableRow";const zo=p.forwardRef(({className:e,...t},n)=>c.jsx("th",{ref:n,className:ae("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));zo.displayName="TableHead";const Uo=p.forwardRef(({className:e,...t},n)=>c.jsx("td",{ref:n,className:ae("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));Uo.displayName="TableCell";const $C=p.forwardRef(({className:e,...t},n)=>c.jsx("caption",{ref:n,className:ae("mt-4 text-sm text-muted-foreground",e),...t}));$C.displayName="TableCaption";function WC(){const e=[{method:"POST",endpoint:"/auth/register",description:"User registration"},{method:"POST",endpoint:"/auth/login",description:"User login"},{method:"POST",endpoint:"/auth/logout",description:"User logout"},{method:"POST",endpoint:"/auth/refresh",description:"Token refresh"}],t=[{method:"GET",endpoint:"/user/",description:"Get user profile"},{method:"PUT",endpoint:"/user/profile",description:"Update user profile"},{method:"POST",endpoint:"/user/skills",description:"Add skills"},{method:"DELETE",endpoint:"/user/skills",description:"Remove skills"},{method:"POST",endpoint:"/user/upload_resume",description:"Upload resume"},{method:"GET",endpoint:"/user/preferences",description:"Get user preferences"},{method:"PATCH",endpoint:"/user/preferences",description:"Update preferences"}],n=[{method:"GET",endpoint:"/job/",description:"Get all jobs"},{method:"GET",endpoint:"/job/{id}",description:"Get specific job"},{method:"POST",endpoint:"/job/bookmark/{id}",description:"Bookmark job"},{method:"DELETE",endpoint:"/job/bookmark/{id}",description:"Remove bookmark"},{method:"POST",endpoint:"/job/application/{id}",description:"Apply for job"},{method:"GET",endpoint:"/job/recommendations",description:"Get job recommendations"}],a=[{method:"GET",endpoint:"/course/search/{skill}",description:"Search courses by skill"},{method:"GET",endpoint:"/course/{id}",description:"Get course details"},{method:"POST",endpoint:"/course/progress",description:"Update learning progress"}],l=[{method:"GET",endpoint:"/admin/jobs",description:"Get all jobs (admin)"},{method:"POST",endpoint:"/admin/jobs/enable",description:"Enable job posting"},{method:"POST",endpoint:"/admin/jobs/disable",description:"Disable job posting"},{method:"GET",endpoint:"/admin/users",description:"Get user statistics"},{method:"POST",endpoint:"/admin/scrape_jobs",description:"Trigger job scraping"}],r=`{
  "email": "<EMAIL>",
  "password": "securepassword123"
}`,i=`{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "user": {
    "id": "uuid-string",
    "email": "<EMAIL>",
    "profile": {
      "first_name": "John",
      "last_name": "Doe"
    }
  }
}`,o=`{
  "user_skills": ["Python", "Machine Learning", "FastAPI"],
  "experience_level": "intermediate",
  "location_preference": "remote",
  "salary_range": {
    "min": 70000,
    "max": 120000
  }
}`,s=`{
  "recommendations": [
    {
      "job_id": "uuid-string",
      "title": "Senior Python Developer",
      "company": "Tech Corp",
      "match_score": 0.87,
      "required_skills": ["Python", "FastAPI", "PostgreSQL"],
      "salary_range": "80k-110k",
      "location": "Remote",
      "description": "We're looking for a skilled Python developer..."
    }
  ],
  "total_matches": 15,
  "page": 1
}`,u=m=>{switch(m){case"GET":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"POST":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"PUT":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"DELETE":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";case"PATCH":return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200"}},d=({endpoints:m})=>c.jsxs(Yb,{children:[c.jsx(Vb,{children:c.jsxs(mf,{children:[c.jsx(zo,{children:"Method"}),c.jsx(zo,{children:"Endpoint"}),c.jsx(zo,{children:"Description"})]})}),c.jsx(Qb,{children:m.map((h,f)=>c.jsxs(mf,{children:[c.jsx(Uo,{children:c.jsx(Ua,{className:u(h.method),children:h.method})}),c.jsx(Uo,{className:"font-mono text-sm",children:h.endpoint}),c.jsx(Uo,{children:h.description})]},f))})]});return c.jsxs("section",{id:"api-endpoints",className:"space-y-8",children:[c.jsxs("div",{children:[c.jsx("h2",{className:"text-3xl font-bold mb-4",children:"API Documentation"}),c.jsx("p",{className:"text-lg text-muted-foreground mb-6",children:"Complete API reference for the Skill Sage backend services. All endpoints require proper authentication unless specified otherwise."})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-8",children:[c.jsxs(ye,{children:[c.jsx(be,{children:c.jsx(xe,{className:"text-lg",children:"Base URL"})}),c.jsx(_e,{children:c.jsx("code",{className:"bg-muted px-2 py-1 rounded text-sm",children:"http://localhost:8004"})})]}),c.jsxs(ye,{children:[c.jsx(be,{children:c.jsx(xe,{className:"text-lg",children:"Authentication"})}),c.jsx(_e,{children:c.jsxs("code",{className:"bg-muted px-2 py-1 rounded text-sm",children:["Bearer ","{token}"]})})]})]}),c.jsxs(Gb,{defaultValue:"auth",className:"w-full",children:[c.jsxs(qd,{className:"grid w-full grid-cols-5",children:[c.jsxs(xn,{value:"auth",className:"flex items-center gap-1",children:[c.jsx(lp,{className:"h-3 w-3"}),"Auth"]}),c.jsxs(xn,{value:"user",className:"flex items-center gap-1",children:[c.jsx(rp,{className:"h-3 w-3"}),"User"]}),c.jsxs(xn,{value:"jobs",className:"flex items-center gap-1",children:[c.jsx(tp,{className:"h-3 w-3"}),"Jobs"]}),c.jsxs(xn,{value:"courses",className:"flex items-center gap-1",children:[c.jsx(np,{className:"h-3 w-3"}),"Courses"]}),c.jsxs(xn,{value:"admin",className:"flex items-center gap-1",children:[c.jsx(ap,{className:"h-3 w-3"}),"Admin"]})]}),c.jsxs(Sn,{value:"auth",className:"space-y-6",children:[c.jsxs(ye,{children:[c.jsxs(be,{children:[c.jsxs(xe,{className:"flex items-center gap-2",children:[c.jsx(lp,{className:"h-5 w-5"}),"Authentication Endpoints"]}),c.jsx(De,{children:"Manage user authentication, registration, and token management."})]}),c.jsx(_e,{children:c.jsx(d,{endpoints:e})})]}),c.jsxs(ye,{children:[c.jsx(be,{children:c.jsx(xe,{children:"Example: User Login"})}),c.jsxs(_e,{className:"space-y-4",children:[c.jsxs("div",{children:[c.jsx("h4",{className:"font-semibold mb-2",children:"Request Body:"}),c.jsx(At,{code:r,language:"json"})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"font-semibold mb-2",children:"Response:"}),c.jsx(At,{code:i,language:"json"})]})]})]})]}),c.jsx(Sn,{value:"user",className:"space-y-6",children:c.jsxs(ye,{children:[c.jsxs(be,{children:[c.jsxs(xe,{className:"flex items-center gap-2",children:[c.jsx(rp,{className:"h-5 w-5"}),"User Management Endpoints"]}),c.jsx(De,{children:"Handle user profiles, skills, preferences, and resume management."})]}),c.jsx(_e,{children:c.jsx(d,{endpoints:t})})]})}),c.jsxs(Sn,{value:"jobs",className:"space-y-6",children:[c.jsxs(ye,{children:[c.jsxs(be,{children:[c.jsxs(xe,{className:"flex items-center gap-2",children:[c.jsx(tp,{className:"h-5 w-5"}),"Job Management Endpoints"]}),c.jsx(De,{children:"Access job listings, recommendations, applications, and bookmarks."})]}),c.jsx(_e,{children:c.jsx(d,{endpoints:n})})]}),c.jsxs(ye,{children:[c.jsx(be,{children:c.jsx(xe,{children:"Example: Job Recommendations"})}),c.jsxs(_e,{className:"space-y-4",children:[c.jsxs("div",{children:[c.jsx("h4",{className:"font-semibold mb-2",children:"Request Body:"}),c.jsx(At,{code:o,language:"json"})]}),c.jsxs("div",{children:[c.jsx("h4",{className:"font-semibold mb-2",children:"Response:"}),c.jsx(At,{code:s,language:"json"})]})]})]})]}),c.jsx(Sn,{value:"courses",className:"space-y-6",children:c.jsxs(ye,{children:[c.jsxs(be,{children:[c.jsxs(xe,{className:"flex items-center gap-2",children:[c.jsx(np,{className:"h-5 w-5"}),"Course Management Endpoints"]}),c.jsx(De,{children:"Discover courses, track learning progress, and get skill-based recommendations."})]}),c.jsx(_e,{children:c.jsx(d,{endpoints:a})})]})}),c.jsx(Sn,{value:"admin",className:"space-y-6",children:c.jsxs(ye,{children:[c.jsxs(be,{children:[c.jsxs(xe,{className:"flex items-center gap-2",children:[c.jsx(ap,{className:"h-5 w-5"}),"Admin Endpoints"]}),c.jsx(De,{children:"Administrative functions for job management, user analytics, and system operations."})]}),c.jsx(_e,{children:c.jsx(d,{endpoints:l})})]})})]})]})}function IC(){const[e,t]=p.useState("overview"),[n,a]=p.useState(""),[l,r]=p.useState(!1);p.useEffect(()=>{const u=localStorage.getItem("theme");(u==="dark"||!u&&window.matchMedia("(prefers-color-scheme: dark)").matches)&&(r(!0),document.documentElement.classList.add("dark"))},[]);const i=()=>{r(!l),l?(document.documentElement.classList.remove("dark"),localStorage.setItem("theme","light")):(document.documentElement.classList.add("dark"),localStorage.setItem("theme","dark"))},o=u=>{t(u);const d=document.getElementById(u);d&&d.scrollIntoView({behavior:"smooth"})},s=[{id:"overview",component:c.jsx(RC,{})},{id:"architecture",component:c.jsx(OC,{})},{id:"technologies",component:c.jsx(ej,{})},{id:"user-journey",component:c.jsx(tj,{})},{id:"backend-docs",component:c.jsx(nj,{})},{id:"frontend-apps",component:c.jsx(aj,{})},{id:"ai-features",component:c.jsx(lj,{})},{id:"installation",component:c.jsx(FC,{})},{id:"api-endpoints",component:c.jsx(WC,{})},{id:"deployment",component:c.jsx(rj,{})}];return c.jsxs("div",{className:"min-h-screen bg-background",children:[c.jsx(NC,{activeSection:e,onSectionClick:o}),c.jsxs("main",{className:"md:ml-80 min-h-screen",children:[c.jsx("header",{className:"sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:c.jsxs("div",{className:"container flex h-16 items-center justify-between px-4 md:px-8",children:[c.jsx("div",{className:"flex items-center gap-4 flex-1 max-w-md",children:c.jsxs("div",{className:"relative flex-1",children:[c.jsx(iN,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),c.jsx(C0,{placeholder:"Search documentation...",value:n,onChange:u=>a(u.target.value),className:"pl-10"})]})}),c.jsx(Wt,{variant:"outline",size:"icon",onClick:i,className:"ml-4",children:l?c.jsx(oN,{className:"h-4 w-4"}):c.jsx(rN,{className:"h-4 w-4"})})]})}),c.jsx(Ud,{className:"h-[calc(100vh-4rem)]",children:c.jsx("div",{className:"container px-4 md:px-8 py-8 max-w-4xl",children:s.map(u=>c.jsx("div",{className:"mb-16",children:u.component},u.id))})})]})]})}function ej(){return c.jsxs("section",{id:"technologies",className:"space-y-6",children:[c.jsx("h2",{className:"text-3xl font-bold",children:"Technologies Used"}),c.jsx("p",{className:"text-lg text-muted-foreground",children:"Comprehensive overview of the technology stack powering Skill Sage."})]})}function tj(){return c.jsxs("section",{id:"user-journey",className:"space-y-6",children:[c.jsx("h2",{className:"text-3xl font-bold",children:"User Journey & Features"}),c.jsx("p",{className:"text-lg text-muted-foreground",children:"Detailed walkthrough of user interactions and platform features."})]})}function nj(){return c.jsxs("section",{id:"backend-docs",className:"space-y-6",children:[c.jsx("h2",{className:"text-3xl font-bold",children:"Backend API Documentation"}),c.jsx("p",{className:"text-lg text-muted-foreground",children:"Core services and API architecture details."})]})}function aj(){return c.jsxs("section",{id:"frontend-apps",className:"space-y-6",children:[c.jsx("h2",{className:"text-3xl font-bold",children:"Frontend Applications"}),c.jsx("p",{className:"text-lg text-muted-foreground",children:"Flutter mobile app and React dashboard architecture."})]})}function lj(){return c.jsxs("section",{id:"ai-features",className:"space-y-6",children:[c.jsx("h2",{className:"text-3xl font-bold",children:"AI & Machine Learning Features"}),c.jsx("p",{className:"text-lg text-muted-foreground",children:"Advanced AI algorithms and machine learning implementations."})]})}function rj(){return c.jsxs("section",{id:"deployment",className:"space-y-6",children:[c.jsx("h2",{className:"text-3xl font-bold",children:"Deployment"}),c.jsx("p",{className:"text-lg text-muted-foreground",children:"Production deployment guides for all platform components."})]})}function ij(){return c.jsx("div",{className:"min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-gray-50 to-blue-50 p-6 text-center",children:c.jsxs("div",{className:"space-y-6 max-w-md",children:[c.jsxs("div",{className:"space-y-3",children:[c.jsx("h1",{className:"text-8xl font-bold text-blue-600",children:"404"}),c.jsx("h2",{className:"text-2xl font-semibold text-gray-800",children:"Page Not Found"}),c.jsx("p",{className:"text-muted-foreground",children:"The page you're looking for doesn't exist or may have been moved."})]}),c.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[c.jsx(Wt,{asChild:!0,children:c.jsx("a",{href:"/",children:"Return Home"})}),c.jsx(Wt,{variant:"outline",onClick:()=>window.history.back(),children:"Go Back"})]})]})})}const oj=new QA,sj=()=>c.jsx(PA,{client:oj,children:c.jsxs(xA,{children:[c.jsx(Cw,{}),c.jsx(zT,{children:c.jsxs(MT,{children:[c.jsx(uf,{path:"/",element:c.jsx(IC,{})}),c.jsx(uf,{path:"*",element:c.jsx(ij,{})})]})})]})});FS.createRoot(document.getElementById("root")).render(c.jsx(sj,{}));
